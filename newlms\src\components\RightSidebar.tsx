import React from 'react';
import Calendar from './Calendar.tsx';
import PerformanceChart from './PerformanceChart.tsx';

const RightSidebar: React.FC = () => {
  const courseActivities = [
    {
      id: 1,
      title: 'How to grow your Product design',
      progress: 13,
      status: 'Enroll'
    },
    {
      id: 2,
      title: 'How to grow your Product design',
      progress: 7,
      status: 'Enroll'
    }
  ];

  const upcomingClass = {
    date: '10.23',
    title: 'Programming Class',
    time: '10:00 • Zoom Meeting',
    instructor: '<PERSON>'
  };

  return (
    <div className="w-full lg:w-80 bg-white lg:border-l border-gray-200 p-4 lg:p-6 space-y-6">
      {/* Course Activity */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Course activity</h3>
          <button className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center text-white hover:bg-blue-700">
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
            </svg>
          </button>
        </div>
        <p className="text-sm text-gray-500 mb-4">Sep 07th, 2023</p>

        <div className="space-y-4">
          <div className="text-sm font-medium text-gray-700 mb-3">Courses</div>
          {courseActivities.map((activity) => (
            <div key={activity.id} className="bg-blue-600 rounded-xl p-4 text-white">
              <h4 className="font-semibold text-sm mb-2">{activity.title}</h4>
              <p className="text-xs text-blue-100 mb-3">Follow those easy and simple steps</p>

              {/* Progress Bar */}
              <div className="mb-3">
                <div className="flex justify-between text-xs mb-1">
                  <span>Progress</span>
                  <span>{activity.progress}%</span>
                </div>
                <div className="w-full bg-blue-500 rounded-full h-2">
                  <div
                    className="bg-orange-400 h-2 rounded-full"
                    style={{ width: `${activity.progress}%` }}
                  ></div>
                </div>
              </div>

              <button className="w-full bg-white text-blue-600 font-semibold py-2 px-4 rounded-lg text-sm hover:bg-blue-50">
                {activity.status}
              </button>
            </div>
          ))}
        </div>
      </div>

      {/* Calendar */}
      <Calendar />

      {/* Upcoming Class */}
      <div>
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Upcoming Class</h3>
        <div className="flex items-center space-x-3 mb-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-900">{upcomingClass.date}</div>
          </div>
          <div className="flex-1">
            <h4 className="font-semibold text-gray-900">{upcomingClass.title}</h4>
            <p className="text-sm text-gray-500">{upcomingClass.time}</p>
          </div>
          <button className="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
            <svg className="w-4 h-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
            </svg>
          </button>
        </div>
        <button className="w-full text-blue-600 font-medium text-sm hover:text-blue-700">
          Show All
        </button>
      </div>

      {/* Performance */}
      <div>
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">Performance</h3>
          <select className="text-sm text-gray-500 border-none bg-transparent focus:outline-none">
            <option>Monthly</option>
            <option>Weekly</option>
            <option>Daily</option>
          </select>
        </div>

        <PerformanceChart />
      </div>
    </div>
  );
};

export default RightSidebar;
