{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Sidebar from './components/Sidebar.tsx';\nimport Header from './components/Header.tsx';\nimport WelcomeSection from './components/WelcomeSection.tsx';\nimport AnalyticsCards from './components/AnalyticsCards.tsx';\nimport FeaturedCourses from './components/FeaturedCourses.tsx';\nimport RightSidebar from './components/RightSidebar.tsx';\nimport ErrorBoundary from './components/ErrorBoundary.tsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex relative\",\n      children: [isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n        onClick: () => setIsMobileMenuOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        isMobileMenuOpen: isMobileMenuOpen,\n        setIsMobileMenuOpen: setIsMobileMenuOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(Header, {\n          onMobileMenuToggle: () => setIsMobileMenuOpen(!isMobileMenuOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex flex-col lg:flex-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 p-4 lg:p-6 space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(WelcomeSection, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 44,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(AnalyticsCards, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(FeaturedCourses, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 50,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:block\",\n            children: /*#__PURE__*/_jsxDEV(RightSidebar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 55,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 54,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 40,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"QerECOS75+B7gv+k3q7FrDf39mc=\");\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "Sidebar", "Header", "WelcomeSection", "AnalyticsCards", "FeaturedCourses", "RightSidebar", "Error<PERSON>ou<PERSON><PERSON>", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "children", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMobileMenuToggle", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/Dashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport Sidebar from './components/Sidebar.tsx';\nimport Header from './components/Header.tsx';\nimport WelcomeSection from './components/WelcomeSection.tsx';\nimport AnalyticsCards from './components/AnalyticsCards.tsx';\nimport FeaturedCourses from './components/FeaturedCourses.tsx';\nimport RightSidebar from './components/RightSidebar.tsx';\nimport ErrorBoundary from './components/ErrorBoundary.tsx';\nimport Toast from './components/Toast.tsx';\nimport { useToast } from './hooks/useToast.tsx';\n\nconst Dashboard: React.FC = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n\n  return (\n    <ErrorBoundary>\n      <div className=\"min-h-screen bg-gray-50 flex relative\">\n        {/* Mobile Sidebar Overlay */}\n        {isMobileMenuOpen && (\n          <div\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n            onClick={() => setIsMobileMenuOpen(false)}\n          />\n        )}\n\n        {/* Left Sidebar */}\n        <Sidebar\n          isMobileMenuOpen={isMobileMenuOpen}\n          setIsMobileMenuOpen={setIsMobileMenuOpen}\n        />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col\">\n          {/* Header */}\n          <Header\n            onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          />\n\n        {/* Main Dashboard Content */}\n        <div className=\"flex-1 flex flex-col lg:flex-row\">\n          {/* Center Content */}\n          <div className=\"flex-1 p-4 lg:p-6 space-y-6\">\n            {/* Welcome Section */}\n            <WelcomeSection />\n\n            {/* Analytics Cards */}\n            <AnalyticsCards />\n\n            {/* Featured Courses */}\n            <FeaturedCourses />\n          </div>\n\n          {/* Right Sidebar */}\n          <div className=\"lg:block\">\n            <RightSidebar />\n          </div>\n          </div>\n        </div>\n      </div>\n    </ErrorBoundary>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,aAAa,MAAM,gCAAgC;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAI3D,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAE/D,oBACES,OAAA,CAACF,aAAa;IAAAO,QAAA,eACZL,OAAA;MAAKM,SAAS,EAAC,uCAAuC;MAAAD,QAAA,GAEnDF,gBAAgB,iBACfH,OAAA;QACEM,SAAS,EAAC,qDAAqD;QAC/DC,OAAO,EAAEA,CAAA,KAAMH,mBAAmB,CAAC,KAAK;MAAE;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CACF,eAGDX,OAAA,CAACR,OAAO;QACNW,gBAAgB,EAAEA,gBAAiB;QACnCC,mBAAmB,EAAEA;MAAoB;QAAAI,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAGFX,OAAA;QAAKM,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBAEnCL,OAAA,CAACP,MAAM;UACLmB,kBAAkB,EAAEA,CAAA,KAAMR,mBAAmB,CAAC,CAACD,gBAAgB;QAAE;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eAGJX,OAAA;UAAKM,SAAS,EAAC,kCAAkC;UAAAD,QAAA,gBAE/CL,OAAA;YAAKM,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAE1CL,OAAA,CAACN,cAAc;cAAAc,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGlBX,OAAA,CAACL,cAAc;cAAAa,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGlBX,OAAA,CAACJ,eAAe;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAGNX,OAAA;YAAKM,SAAS,EAAC,UAAU;YAAAD,QAAA,eACvBL,OAAA,CAACH,YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAACT,EAAA,CAlDID,SAAmB;AAAAY,EAAA,GAAnBZ,SAAmB;AAoDzB,eAAeA,SAAS;AAAC,IAAAY,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}