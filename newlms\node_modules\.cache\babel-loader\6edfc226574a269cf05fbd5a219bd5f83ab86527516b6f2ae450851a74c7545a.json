{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = () => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white border-b border-gray-200 px-6 py-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowMobileMenu(!showMobileMenu),\n          className: \"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M4 6h16M4 12h16M4 18h16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 20,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 19,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 15,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 14,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden lg:block\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Wednesday\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: \"September 6, 2023\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 32,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 max-w-md mx-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-5 w-5 text-gray-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 41,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 39,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search for course\",\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowNotifications(!showNotifications),\n            className: \"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 63,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 62,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 65,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 rounded-full bg-gray-300 overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80\",\n              alt: \"Profile\",\n              className: \"w-full h-full object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 72,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 71,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-900\",\n              children: \"Corey George\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Student\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 80,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 70,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 12,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"wcQ60pRSGBp81izZmRuUp961CqM=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Header", "_s", "searchQuery", "setSearch<PERSON>uery", "showNotifications", "setShowNotifications", "showMobileMenu", "setShowMobileMenu", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "e", "target", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/Header.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst Header: React.FC = () => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [showMobileMenu, setShowMobileMenu] = useState(false);\n\n\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Mobile Menu Button */}\n        <div className=\"lg:hidden\">\n          <button\n            onClick={() => setShowMobileMenu(!showMobileMenu)}\n            className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\"\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Date and Welcome */}\n        <div className=\"hidden lg:block\">\n          <h1 className=\"text-lg font-semibold text-gray-900\">Wednesday</h1>\n          <p className=\"text-sm text-gray-500\">September 6, 2023</p>\n        </div>\n\n        {/* Mobile Title */}\n        <div className=\"lg:hidden\">\n          <h1 className=\"text-lg font-semibold text-gray-900\">Dashboard</h1>\n        </div>\n\n        {/* Search Bar */}\n        <div className=\"flex-1 max-w-md mx-8\">\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search for course\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n        </div>\n\n        {/* Right Side - Notifications and Profile */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Notifications */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowNotifications(!showNotifications)}\n              className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z\" />\n              </svg>\n              <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"></span>\n            </button>\n          </div>\n\n          {/* Profile */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 rounded-full bg-gray-300 overflow-hidden\">\n              <img\n                src=\"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80\"\n                alt=\"Profile\"\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n            <div className=\"hidden md:block\">\n              <p className=\"text-sm font-medium text-gray-900\">Corey George</p>\n              <p className=\"text-xs text-gray-500\">Student</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,MAAgB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC7B,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGN,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACO,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGR,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACS,cAAc,EAAEC,iBAAiB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EAI3D,oBACEE,OAAA;IAAQS,SAAS,EAAC,6CAA6C;IAAAC,QAAA,eAC7DV,OAAA;MAAKS,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAEhDV,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBV,OAAA;UACEW,OAAO,EAAEA,CAAA,KAAMH,iBAAiB,CAAC,CAACD,cAAc,CAAE;UAClDE,SAAS,EAAC,0HAA0H;UAAAC,QAAA,eAEpIV,OAAA;YAAKS,SAAS,EAAC,SAAS;YAACG,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5EV,OAAA;cAAMe,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNtB,OAAA;QAAKS,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BV,OAAA;UAAIS,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEtB,OAAA;UAAGS,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAiB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAGNtB,OAAA;QAAKS,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBV,OAAA;UAAIS,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAGNtB,OAAA;QAAKS,SAAS,EAAC,sBAAsB;QAAAC,QAAA,eACnCV,OAAA;UAAKS,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBV,OAAA;YAAKS,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFV,OAAA;cAAKS,SAAS,EAAC,uBAAuB;cAACG,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC1FV,OAAA;gBAAMe,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNtB,OAAA;YACEuB,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,mBAAmB;YAC/BC,KAAK,EAAEtB,WAAY;YACnBuB,QAAQ,EAAGC,CAAC,IAAKvB,cAAc,CAACuB,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDhB,SAAS,EAAC;UAA+M;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1N,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNtB,OAAA;QAAKS,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1CV,OAAA;UAAKS,SAAS,EAAC,UAAU;UAAAC,QAAA,eACvBV,OAAA;YACEW,OAAO,EAAEA,CAAA,KAAML,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;YACxDI,SAAS,EAAC,mIAAmI;YAAAC,QAAA,gBAE7IV,OAAA;cAAKS,SAAS,EAAC,SAAS;cAACG,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC5EV,OAAA;gBAAMe,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAmG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxK,CAAC,eACNtB,OAAA;cAAMS,SAAS,EAAC;YAAgF;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eAGNtB,OAAA;UAAKS,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CV,OAAA;YAAKS,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eACjEV,OAAA;cACE6B,GAAG,EAAC,2JAA2J;cAC/JC,GAAG,EAAC,SAAS;cACbrB,SAAS,EAAC;YAA4B;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNtB,OAAA;YAAKS,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BV,OAAA;cAAGS,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjEtB,OAAA;cAAGS,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACpB,EAAA,CApFID,MAAgB;AAAA8B,EAAA,GAAhB9B,MAAgB;AAsFtB,eAAeA,MAAM;AAAC,IAAA8B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}