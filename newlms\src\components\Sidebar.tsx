import React, { useState } from 'react';

const Sidebar: React.FC = () => {
  const [activeItem, setActiveItem] = useState('Overview');

  const menuItems = [
    { name: 'Overview', icon: '📊', active: true },
    { name: 'Account', icon: '👤', active: false },
    { name: 'Notes', icon: '📝', active: false },
    { name: 'Community', icon: '👥', active: false },
    { name: 'Learning', icon: '📚', active: false },
    { name: 'Setting', icon: '⚙️', active: false },
  ];

  return (
    <div className="w-64 bg-white border-r border-gray-200 flex flex-col hidden lg:flex">
      {/* Logo */}
      <div className="p-6 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center">
            <span className="text-white font-bold text-sm">🎓</span>
          </div>
          <span className="text-xl font-bold text-gray-900">Edulearn</span>
        </div>
      </div>

      {/* Navigation Menu */}
      <nav className="flex-1 p-4">
        <ul className="space-y-2">
          {menuItems.map((item) => (
            <li key={item.name}>
              <button
                onClick={() => setActiveItem(item.name)}
                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${
                  activeItem === item.name
                    ? 'bg-blue-50 text-blue-700 border border-blue-200'
                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'
                }`}
              >
                <span className="text-lg">{item.icon}</span>
                <span className="font-medium">{item.name}</span>
              </button>
            </li>
          ))}
        </ul>
      </nav>

      {/* Premium Section */}
      <div className="p-4">
        <div className="bg-gradient-to-br from-blue-600 to-purple-700 rounded-xl p-4 text-white relative overflow-hidden">
          <div className="relative z-10">
            <div className="flex items-center space-x-3 mb-3">
              <div className="w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center">
                <span className="text-2xl">📚</span>
              </div>
              <div>
                <h3 className="font-bold text-sm">Get Premium</h3>
                <p className="text-xs text-blue-100">Lot of services</p>
              </div>
            </div>
            <button className="w-full bg-white text-blue-600 font-semibold py-2 px-4 rounded-lg text-sm hover:bg-blue-50 transition-colors">
              Subscribe Now
            </button>
          </div>
          <div className="absolute -top-4 -right-4 w-16 h-16 bg-white/10 rounded-full"></div>
          <div className="absolute -bottom-2 -left-2 w-12 h-12 bg-white/10 rounded-full"></div>
        </div>
      </div>
    </div>
  );
};

export default Sidebar;
