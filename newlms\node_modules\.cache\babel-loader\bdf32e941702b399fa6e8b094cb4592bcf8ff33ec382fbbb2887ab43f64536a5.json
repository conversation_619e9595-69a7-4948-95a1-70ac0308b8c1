{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\FeaturedCourses.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FeaturedCourses = () => {\n  _s();\n  const [savedCourses, setSavedCourses] = React.useState([2]);\n  const [searchTerm, setSearchTerm] = React.useState('');\n  const [sortBy, setSortBy] = React.useState('name');\n  const toggleSaved = courseId => {\n    setSavedCourses(prev => prev.includes(courseId) ? prev.filter(id => id !== courseId) : [...prev, courseId]);\n  };\n  const courses = [{\n    id: 1,\n    name: 'Basics of Mobile UX',\n    instructor: '<PERSON>',\n    instructorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',\n    startDate: 'Feb 12',\n    progress: '15/20',\n    type: 'UI DESIGN',\n    category: 'Design',\n    saved: false\n  }, {\n    id: 2,\n    name: 'Digital Design System',\n    instructor: 'Bruno Scott',\n    instructorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',\n    startDate: 'Feb 14',\n    progress: '07/02',\n    type: 'UI DESIGN',\n    category: 'Design',\n    saved: true\n  }];\n\n  // Filter and sort courses\n  const filteredCourses = courses.filter(course => course.name.toLowerCase().includes(searchTerm.toLowerCase()) || course.instructor.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b) => {\n    switch (sortBy) {\n      case 'name':\n        return a.name.localeCompare(b.name);\n      case 'date':\n        return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();\n      case 'progress':\n        return parseInt(a.progress.split('/')[0]) - parseInt(b.progress.split('/')[0]);\n      default:\n        return 0;\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-xl border border-gray-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 border-b border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Featured Courses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-sm text-blue-600 hover:text-blue-700 font-medium\",\n          children: \"View all \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 78,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 max-w-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-4 w-4 text-gray-400\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 89,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 88,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 87,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search courses...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 92,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: sortBy,\n          onChange: e => setSortBy(e.target.value),\n          className: \"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"name\",\n            children: \"Sort by Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 107,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"date\",\n            children: \"Sort by Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 108,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"progress\",\n            children: \"Sort by Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 109,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 102,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Course name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 119,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Start\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 122,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 125,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 128,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 131,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 117,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          className: \"bg-white divide-y divide-gray-200\",\n          children: filteredCourses.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: 5,\n              className: \"px-6 py-8 text-center text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-12 h-12 text-gray-300 mb-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20a7.962 7.962 0 01-6-2.709M3 12a9 9 0 1118 0 9 9 0 01-18 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 142,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 141,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"No courses found matching your search.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 144,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 140,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 139,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 138,\n            columnNumber: 15\n          }, this) : filteredCourses.map(course => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: \"hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 h-10 w-10\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-600 font-semibold text-sm\",\n                      children: \"\\uD83D\\uDCF1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 155,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 154,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 153,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: course.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 159,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      className: \"h-5 w-5 rounded-full\",\n                      src: course.instructorAvatar,\n                      alt: course.instructor\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 161,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-2 text-sm text-gray-500\",\n                      children: course.instructor\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 166,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 160,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 158,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 152,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n              children: course.startDate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 171,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n              children: course.progress\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 174,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                children: course.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 178,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 177,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleSaved(course.id),\n                className: \"text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n                children: savedCourses.includes(course.id) ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-yellow-400 hover:text-yellow-500\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 189,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 188,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 hover:text-yellow-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 193,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 183,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 182,\n              columnNumber: 17\n            }, this)]\n          }, course.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 136,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 116,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 115,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 73,\n    columnNumber: 5\n  }, this);\n};\n_s(FeaturedCourses, \"BB3ApXuiol//j4HzBIn1/Y5P5KQ=\");\n_c = FeaturedCourses;\nexport default FeaturedCourses;\nvar _c;\n$RefreshReg$(_c, \"FeaturedCourses\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "FeaturedCourses", "_s", "savedCourses", "setSavedCourses", "useState", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "toggleSaved", "courseId", "prev", "includes", "filter", "id", "courses", "name", "instructor", "<PERSON><PERSON><PERSON><PERSON>", "startDate", "progress", "type", "category", "saved", "filteredCourses", "course", "toLowerCase", "sort", "a", "b", "localeCompare", "Date", "getTime", "parseInt", "split", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "placeholder", "value", "onChange", "e", "target", "length", "colSpan", "map", "src", "alt", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/FeaturedCourses.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface Course {\n  id: number;\n  name: string;\n  instructor: string;\n  instructorAvatar: string;\n  startDate: string;\n  progress: string;\n  type: string;\n  category: string;\n  saved: boolean;\n}\n\nconst FeaturedCourses: React.FC = () => {\n  const [savedCourses, setSavedCourses] = React.useState<number[]>([2]);\n  const [searchTerm, setSearchTerm] = React.useState('');\n  const [sortBy, setSortBy] = React.useState<'name' | 'date' | 'progress'>('name');\n\n  const toggleSaved = (courseId: number) => {\n    setSavedCourses(prev =>\n      prev.includes(courseId)\n        ? prev.filter(id => id !== courseId)\n        : [...prev, courseId]\n    );\n  };\n\n  const courses: Course[] = [\n    {\n      id: 1,\n      name: 'Basics of Mobile UX',\n      instructor: '<PERSON>',\n      instructorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',\n      startDate: 'Feb 12',\n      progress: '15/20',\n      type: 'UI DESIGN',\n      category: 'Design',\n      saved: false\n    },\n    {\n      id: 2,\n      name: 'Digital Design System',\n      instructor: '<PERSON>',\n      instructorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',\n      startDate: 'Feb 14',\n      progress: '07/02',\n      type: 'UI DESIGN',\n      category: 'Design',\n      saved: true\n    }\n  ];\n\n  // Filter and sort courses\n  const filteredCourses = courses\n    .filter(course =>\n      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      course.instructor.toLowerCase().includes(searchTerm.toLowerCase())\n    )\n    .sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'date':\n          return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();\n        case 'progress':\n          return parseInt(a.progress.split('/')[0]) - parseInt(b.progress.split('/')[0]);\n        default:\n          return 0;\n      }\n    });\n\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200\">\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Featured Courses</h2>\n          <button className=\"text-sm text-blue-600 hover:text-blue-700 font-medium\">\n            View all →\n          </button>\n        </div>\n\n        {/* Search and Filter */}\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex-1 max-w-sm\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <svg className=\"h-4 w-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                </svg>\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search courses...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n          </div>\n\n          <select\n            value={sortBy}\n            onChange={(e) => setSortBy(e.target.value as 'name' | 'date' | 'progress')}\n            className=\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n          >\n            <option value=\"name\">Sort by Name</option>\n            <option value=\"date\">Sort by Date</option>\n            <option value=\"progress\">Sort by Progress</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"w-full\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Course name\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Start\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Course\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Type\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Save\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {filteredCourses.length === 0 ? (\n              <tr>\n                <td colSpan={5} className=\"px-6 py-8 text-center text-gray-500\">\n                  <div className=\"flex flex-col items-center\">\n                    <svg className=\"w-12 h-12 text-gray-300 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20a7.962 7.962 0 01-6-2.709M3 12a9 9 0 1118 0 9 9 0 01-18 0z\" />\n                    </svg>\n                    <p className=\"text-sm\">No courses found matching your search.</p>\n                  </div>\n                </td>\n              </tr>\n            ) : (\n              filteredCourses.map((course) => (\n              <tr key={course.id} className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0 h-10 w-10\">\n                      <div className=\"h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center\">\n                        <span className=\"text-blue-600 font-semibold text-sm\">📱</span>\n                      </div>\n                    </div>\n                    <div className=\"ml-4\">\n                      <div className=\"text-sm font-medium text-gray-900\">{course.name}</div>\n                      <div className=\"flex items-center mt-1\">\n                        <img\n                          className=\"h-5 w-5 rounded-full\"\n                          src={course.instructorAvatar}\n                          alt={course.instructor}\n                        />\n                        <span className=\"ml-2 text-sm text-gray-500\">{course.instructor}</span>\n                      </div>\n                    </div>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {course.startDate}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {course.progress}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">\n                    {course.type}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  <button\n                    onClick={() => toggleSaved(course.id)}\n                    className=\"text-gray-400 hover:text-gray-600 transition-colors duration-200\"\n                  >\n                    {savedCourses.includes(course.id) ? (\n                      <svg className=\"w-5 h-5 text-yellow-400 hover:text-yellow-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                      </svg>\n                    ) : (\n                      <svg className=\"w-5 h-5 hover:text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\" />\n                      </svg>\n                    )}\n                  </button>\n                </td>\n              </tr>\n              ))\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n};\n\nexport default FeaturedCourses;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAc1B,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGN,KAAK,CAACO,QAAQ,CAAW,CAAC,CAAC,CAAC,CAAC;EACrE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGT,KAAK,CAACO,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACG,MAAM,EAAEC,SAAS,CAAC,GAAGX,KAAK,CAACO,QAAQ,CAA+B,MAAM,CAAC;EAEhF,MAAMK,WAAW,GAAIC,QAAgB,IAAK;IACxCP,eAAe,CAACQ,IAAI,IAClBA,IAAI,CAACC,QAAQ,CAACF,QAAQ,CAAC,GACnBC,IAAI,CAACE,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKJ,QAAQ,CAAC,GAClC,CAAC,GAAGC,IAAI,EAAED,QAAQ,CACxB,CAAC;EACH,CAAC;EAED,MAAMK,OAAiB,GAAG,CACxB;IACED,EAAE,EAAE,CAAC;IACLE,IAAI,EAAE,qBAAqB;IAC3BC,UAAU,EAAE,aAAa;IACzBC,gBAAgB,EAAE,2FAA2F;IAC7GC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLE,IAAI,EAAE,uBAAuB;IAC7BC,UAAU,EAAE,aAAa;IACzBC,gBAAgB,EAAE,2FAA2F;IAC7GC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMC,eAAe,GAAGT,OAAO,CAC5BF,MAAM,CAACY,MAAM,IACZA,MAAM,CAACT,IAAI,CAACU,WAAW,CAAC,CAAC,CAACd,QAAQ,CAACP,UAAU,CAACqB,WAAW,CAAC,CAAC,CAAC,IAC5DD,MAAM,CAACR,UAAU,CAACS,WAAW,CAAC,CAAC,CAACd,QAAQ,CAACP,UAAU,CAACqB,WAAW,CAAC,CAAC,CACnE,CAAC,CACAC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACd,QAAQtB,MAAM;MACZ,KAAK,MAAM;QACT,OAAOqB,CAAC,CAACZ,IAAI,CAACc,aAAa,CAACD,CAAC,CAACb,IAAI,CAAC;MACrC,KAAK,MAAM;QACT,OAAO,IAAIe,IAAI,CAACH,CAAC,CAACT,SAAS,CAAC,CAACa,OAAO,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACV,SAAS,CAAC,CAACa,OAAO,CAAC,CAAC;MAC1E,KAAK,UAAU;QACb,OAAOC,QAAQ,CAACL,CAAC,CAACR,QAAQ,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,QAAQ,CAACJ,CAAC,CAACT,QAAQ,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChF;QACE,OAAO,CAAC;IACZ;EACF,CAAC,CAAC;EAEJ,oBACEnC,OAAA;IAAKoC,SAAS,EAAC,4CAA4C;IAAAC,QAAA,gBAEzDrC,OAAA;MAAKoC,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CrC,OAAA;QAAKoC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDrC,OAAA;UAAIoC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzEzC,OAAA;UAAQoC,SAAS,EAAC,uDAAuD;UAAAC,QAAA,EAAC;QAE1E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNzC,OAAA;QAAKoC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CrC,OAAA;UAAKoC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BrC,OAAA;YAAKoC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBrC,OAAA;cAAKoC,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnFrC,OAAA;gBAAKoC,SAAS,EAAC,uBAAuB;gBAACM,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAP,QAAA,eAC1FrC,OAAA;kBAAM6C,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA6C;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNzC,OAAA;cACEsB,IAAI,EAAC,MAAM;cACX2B,WAAW,EAAC,mBAAmB;cAC/BC,KAAK,EAAE5C,UAAW;cAClB6C,QAAQ,EAAGC,CAAC,IAAK7C,aAAa,CAAC6C,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/Cd,SAAS,EAAC;YAAqN;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAENzC,OAAA;UACEkD,KAAK,EAAE1C,MAAO;UACd2C,QAAQ,EAAGC,CAAC,IAAK3C,SAAS,CAAC2C,CAAC,CAACC,MAAM,CAACH,KAAqC,CAAE;UAC3Ed,SAAS,EAAC,+HAA+H;UAAAC,QAAA,gBAEzIrC,OAAA;YAAQkD,KAAK,EAAC,MAAM;YAAAb,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CzC,OAAA;YAAQkD,KAAK,EAAC,MAAM;YAAAb,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1CzC,OAAA;YAAQkD,KAAK,EAAC,UAAU;YAAAb,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNzC,OAAA;MAAKoC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BrC,OAAA;QAAOoC,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACvBrC,OAAA;UAAOoC,SAAS,EAAC,YAAY;UAAAC,QAAA,eAC3BrC,OAAA;YAAAqC,QAAA,gBACErC,OAAA;cAAIoC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLzC,OAAA;cAAIoC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLzC,OAAA;cAAIoC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLzC,OAAA;cAAIoC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACLzC,OAAA;cAAIoC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACRzC,OAAA;UAAOoC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EACjDZ,eAAe,CAAC6B,MAAM,KAAK,CAAC,gBAC3BtD,OAAA;YAAAqC,QAAA,eACErC,OAAA;cAAIuD,OAAO,EAAE,CAAE;cAACnB,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAC7DrC,OAAA;gBAAKoC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCrC,OAAA;kBAAKoC,SAAS,EAAC,8BAA8B;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACjGrC,OAAA;oBAAM6C,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAqI;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1M,CAAC,eACNzC,OAAA;kBAAGoC,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAsC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GAELhB,eAAe,CAAC+B,GAAG,CAAE9B,MAAM,iBAC3B1B,OAAA;YAAoBoC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC9CrC,OAAA;cAAIoC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eACzCrC,OAAA;gBAAKoC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCrC,OAAA;kBAAKoC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,eACtCrC,OAAA;oBAAKoC,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChFrC,OAAA;sBAAMoC,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACNzC,OAAA;kBAAKoC,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBrC,OAAA;oBAAKoC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEX,MAAM,CAACT;kBAAI;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtEzC,OAAA;oBAAKoC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCrC,OAAA;sBACEoC,SAAS,EAAC,sBAAsB;sBAChCqB,GAAG,EAAE/B,MAAM,CAACP,gBAAiB;sBAC7BuC,GAAG,EAAEhC,MAAM,CAACR;oBAAW;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACFzC,OAAA;sBAAMoC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAEX,MAAM,CAACR;oBAAU;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACLzC,OAAA;cAAIoC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DX,MAAM,CAACN;YAAS;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACLzC,OAAA;cAAIoC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DX,MAAM,CAACL;YAAQ;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACLzC,OAAA;cAAIoC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eACzCrC,OAAA;gBAAMoC,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EACjGX,MAAM,CAACJ;cAAI;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACLzC,OAAA;cAAIoC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,eAC/DrC,OAAA;gBACE2D,OAAO,EAAEA,CAAA,KAAMjD,WAAW,CAACgB,MAAM,CAACX,EAAE,CAAE;gBACtCqB,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,EAE3ElC,YAAY,CAACU,QAAQ,CAACa,MAAM,CAACX,EAAE,CAAC,gBAC/Bf,OAAA;kBAAKoC,SAAS,EAAC,+CAA+C;kBAACM,IAAI,EAAC,cAAc;kBAACE,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACpGrC,OAAA;oBAAMgD,CAAC,EAAC;kBAA0V;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClW,CAAC,gBAENzC,OAAA;kBAAKoC,SAAS,EAAC,+BAA+B;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAClGrC,OAAA;oBAAM6C,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAmD;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA,GA/CEf,MAAM,CAACX,EAAE;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgDd,CACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACvC,EAAA,CA/LID,eAAyB;AAAA2D,EAAA,GAAzB3D,eAAyB;AAiM/B,eAAeA,eAAe;AAAC,IAAA2D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}