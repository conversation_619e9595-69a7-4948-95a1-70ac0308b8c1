{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\AnalyticsCards.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalyticsCard = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  color,\n  bgColor\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-300 cursor-pointer group\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-12 h-12 ${bgColor} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-xl ${color}`,\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 17,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-right\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: subtitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 22,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 20,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-sm font-medium text-gray-600 group-hover:text-gray-800 transition-colors duration-300\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 25,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 15,\n    columnNumber: 5\n  }, this);\n};\n_c = AnalyticsCard;\nconst AnalyticsCards = () => {\n  _s();\n  const [loading, setLoading] = useState(true);\n\n  // Simulate loading data\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 1000);\n    return () => clearTimeout(timer);\n  }, []);\n  const analyticsData = [{\n    title: 'Weekly Analysis',\n    value: '02',\n    subtitle: '5 Min 10 Sec',\n    icon: '📊',\n    color: 'text-blue-600',\n    bgColor: 'bg-blue-100'\n  }, {\n    title: 'Total Course',\n    value: '04',\n    subtitle: '5 Min 7 Sec',\n    icon: '📚',\n    color: 'text-orange-600',\n    bgColor: 'bg-orange-100'\n  }, {\n    title: 'Course Enroll',\n    value: '3.69k',\n    subtitle: '5 Min 7 Sec',\n    icon: '👥',\n    color: 'text-purple-600',\n    bgColor: 'bg-purple-100'\n  }];\n  if (loading) {\n    return /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n      children: [1, 2, 3].map(index => /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-white rounded-xl p-6 border border-gray-200\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-between mb-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 74,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-right\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-16 h-8 bg-gray-200 rounded animate-pulse mb-2\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-20 h-4 bg-gray-200 rounded animate-pulse\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 77,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 75,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-24 h-4 bg-gray-200 rounded animate-pulse\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 80,\n          columnNumber: 13\n        }, this)]\n      }, index, true, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 11\n      }, this))\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this);\n  }\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n    children: analyticsData.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"animate-slide-up\",\n      style: {\n        animationDelay: `${index * 0.1}s`\n      },\n      children: /*#__PURE__*/_jsxDEV(AnalyticsCard, {\n        ...item\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 91,\n        columnNumber: 11\n      }, this)\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 90,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 88,\n    columnNumber: 5\n  }, this);\n};\n_s(AnalyticsCards, \"J7PPXooW06IQ11rfabbvgk72KFw=\");\n_c2 = AnalyticsCards;\nexport default AnalyticsCards;\nvar _c, _c2;\n$RefreshReg$(_c, \"AnalyticsCard\");\n$RefreshReg$(_c2, \"AnalyticsCards\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "jsxDEV", "_jsxDEV", "AnalyticsCard", "title", "value", "subtitle", "icon", "color", "bgColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AnalyticsCards", "_s", "loading", "setLoading", "timer", "setTimeout", "clearTimeout", "analyticsData", "map", "index", "item", "style", "animationDelay", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/AnalyticsCards.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\nimport LoadingSpinner from './LoadingSpinner.tsx';\n\ninterface AnalyticsCardProps {\n  title: string;\n  value: string;\n  subtitle: string;\n  icon: string;\n  color: string;\n  bgColor: string;\n}\n\nconst AnalyticsCard: React.FC<AnalyticsCardProps> = ({ title, value, subtitle, icon, color, bgColor }) => {\n  return (\n    <div className=\"bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-300 cursor-pointer group\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className={`w-12 h-12 ${bgColor} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\n          <span className={`text-xl ${color}`}>{icon}</span>\n        </div>\n        <div className=\"text-right\">\n          <div className=\"text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300\">{value}</div>\n          <div className=\"text-sm text-gray-500\">{subtitle}</div>\n        </div>\n      </div>\n      <h3 className=\"text-sm font-medium text-gray-600 group-hover:text-gray-800 transition-colors duration-300\">{title}</h3>\n    </div>\n  );\n};\n\nconst AnalyticsCards: React.FC = () => {\n  const [loading, setLoading] = useState(true);\n\n  // Simulate loading data\n  useEffect(() => {\n    const timer = setTimeout(() => {\n      setLoading(false);\n    }, 1000);\n    return () => clearTimeout(timer);\n  }, []);\n\n  const analyticsData = [\n    {\n      title: 'Weekly Analysis',\n      value: '02',\n      subtitle: '5 Min 10 Sec',\n      icon: '📊',\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-100'\n    },\n    {\n      title: 'Total Course',\n      value: '04',\n      subtitle: '5 Min 7 Sec',\n      icon: '📚',\n      color: 'text-orange-600',\n      bgColor: 'bg-orange-100'\n    },\n    {\n      title: 'Course Enroll',\n      value: '3.69k',\n      subtitle: '5 Min 7 Sec',\n      icon: '👥',\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-100'\n    }\n  ];\n\n  if (loading) {\n    return (\n      <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n        {[1, 2, 3].map((index) => (\n          <div key={index} className=\"bg-white rounded-xl p-6 border border-gray-200\">\n            <div className=\"flex items-center justify-between mb-4\">\n              <div className=\"w-12 h-12 bg-gray-200 rounded-full animate-pulse\"></div>\n              <div className=\"text-right\">\n                <div className=\"w-16 h-8 bg-gray-200 rounded animate-pulse mb-2\"></div>\n                <div className=\"w-20 h-4 bg-gray-200 rounded animate-pulse\"></div>\n              </div>\n            </div>\n            <div className=\"w-24 h-4 bg-gray-200 rounded animate-pulse\"></div>\n          </div>\n        ))}\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n      {analyticsData.map((item, index) => (\n        <div key={index} className=\"animate-slide-up\" style={{ animationDelay: `${index * 0.1}s` }}>\n          <AnalyticsCard {...item} />\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default AnalyticsCards;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAYnD,MAAMC,aAA2C,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,IAAI;EAAEC,KAAK;EAAEC;AAAQ,CAAC,KAAK;EACxG,oBACEP,OAAA;IAAKQ,SAAS,EAAC,uIAAuI;IAAAC,QAAA,gBACpJT,OAAA;MAAKQ,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDT,OAAA;QAAKQ,SAAS,EAAE,aAAaD,OAAO,wGAAyG;QAAAE,QAAA,eAC3IT,OAAA;UAAMQ,SAAS,EAAE,WAAWF,KAAK,EAAG;UAAAG,QAAA,EAAEJ;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNb,OAAA;QAAKQ,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBT,OAAA;UAAKQ,SAAS,EAAC,2FAA2F;UAAAC,QAAA,EAAEN;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxHb,OAAA;UAAKQ,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAEL;QAAQ;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNb,OAAA;MAAIQ,SAAS,EAAC,4FAA4F;MAAAC,QAAA,EAAEP;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpH,CAAC;AAEV,CAAC;AAACC,EAAA,GAfIb,aAA2C;AAiBjD,MAAMc,cAAwB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACrC,MAAM,CAACC,OAAO,EAAEC,UAAU,CAAC,GAAGrB,QAAQ,CAAC,IAAI,CAAC;;EAE5C;EACAC,SAAS,CAAC,MAAM;IACd,MAAMqB,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BF,UAAU,CAAC,KAAK,CAAC;IACnB,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMG,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMG,aAAa,GAAG,CACpB;IACEpB,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,eAAe;IACtBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE;EACX,CAAC,CACF;EAED,IAAIU,OAAO,EAAE;IACX,oBACEjB,OAAA;MAAKQ,SAAS,EAAC,uCAAuC;MAAAC,QAAA,EACnD,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,CAACc,GAAG,CAAEC,KAAK,iBACnBxB,OAAA;QAAiBQ,SAAS,EAAC,gDAAgD;QAAAC,QAAA,gBACzET,OAAA;UAAKQ,SAAS,EAAC,wCAAwC;UAAAC,QAAA,gBACrDT,OAAA;YAAKQ,SAAS,EAAC;UAAkD;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eACxEb,OAAA;YAAKQ,SAAS,EAAC,YAAY;YAAAC,QAAA,gBACzBT,OAAA;cAAKQ,SAAS,EAAC;YAAiD;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC,eACvEb,OAAA;cAAKQ,SAAS,EAAC;YAA4C;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC/D,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eACNb,OAAA;UAAKQ,SAAS,EAAC;QAA4C;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA,GAR1DW,KAAK;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OASV,CACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC,CAAC;EAEV;EAEA,oBACEb,OAAA;IAAKQ,SAAS,EAAC,uCAAuC;IAAAC,QAAA,EACnDa,aAAa,CAACC,GAAG,CAAC,CAACE,IAAI,EAAED,KAAK,kBAC7BxB,OAAA;MAAiBQ,SAAS,EAAC,kBAAkB;MAACkB,KAAK,EAAE;QAAEC,cAAc,EAAE,GAAGH,KAAK,GAAG,GAAG;MAAI,CAAE;MAAAf,QAAA,eACzFT,OAAA,CAACC,aAAa;QAAA,GAAKwB;MAAI;QAAAf,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC,GADnBW,KAAK;MAAAd,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEV,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACG,EAAA,CAlEID,cAAwB;AAAAa,GAAA,GAAxBb,cAAwB;AAoE9B,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAc,GAAA;AAAAC,YAAA,CAAAf,EAAA;AAAAe,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}