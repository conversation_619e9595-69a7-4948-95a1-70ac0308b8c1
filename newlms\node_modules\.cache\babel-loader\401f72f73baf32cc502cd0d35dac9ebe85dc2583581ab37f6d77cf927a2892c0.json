{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\PerformanceChart.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst PerformanceChart = () => {\n  _s();\n  const [animatedProgress, setAnimatedProgress] = useState(0);\n  const performanceData = {\n    points: 8966,\n    progress: 75,\n    // percentage\n    pointsProgress: 'Point Progress',\n    pointsDecrease: 'Point Decrease'\n  };\n  const radius = 60;\n  const strokeWidth = 8;\n  const normalizedRadius = radius - strokeWidth * 2;\n  const circumference = normalizedRadius * 2 * Math.PI;\n  const strokeDasharray = `${circumference} ${circumference}`;\n  const strokeDashoffset = circumference - performanceData.progress / 100 * circumference;\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"text-center\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-center space-x-4 mb-6\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-3 h-3 bg-orange-400 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 24,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-gray-600\",\n          children: performanceData.pointsProgress\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 25,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-2\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-3 h-3 bg-gray-300 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 28,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xs text-gray-600\",\n          children: performanceData.pointsDecrease\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 29,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 27,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative inline-flex items-center justify-center\",\n      children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n        height: radius * 2,\n        width: radius * 2,\n        className: \"transform -rotate-90\",\n        children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n          stroke: \"#e5e7eb\",\n          fill: \"transparent\",\n          strokeWidth: strokeWidth,\n          r: normalizedRadius,\n          cx: radius,\n          cy: radius\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 41,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"circle\", {\n          stroke: \"#fb923c\",\n          fill: \"transparent\",\n          strokeWidth: strokeWidth,\n          strokeDasharray: strokeDasharray,\n          style: {\n            strokeDashoffset\n          },\n          strokeLinecap: \"round\",\n          r: normalizedRadius,\n          cx: radius,\n          cy: radius,\n          className: \"transition-all duration-300 ease-in-out\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 35,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"absolute inset-0 flex flex-col items-center justify-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-gray-900\",\n          children: performanceData.points.toLocaleString()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-500\",\n          children: \"Your Point\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 67,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 65,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"mt-6 flex items-center justify-center space-x-8\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-blue-600 text-lg\",\n            children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 76,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 75,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 74,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2 relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-orange-600 text-xl\",\n            children: \"\\u2B50\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute -top-1 -right-1 w-4 h-4 bg-orange-400 rounded-full\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 81,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-gray-600 text-lg\",\n            children: \"\\uD83D\\uDC69\\u200D\\uD83D\\uDCBC\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 91,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 90,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 89,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 72,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 20,\n    columnNumber: 5\n  }, this);\n};\n_s(PerformanceChart, \"/urh6En+An4josYYa7cvxzY+CNo=\");\n_c = PerformanceChart;\nexport default PerformanceChart;\nvar _c;\n$RefreshReg$(_c, \"PerformanceChart\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Performance<PERSON>hart", "_s", "animatedProgress", "setAnimatedProgress", "performanceData", "points", "progress", "pointsProgress", "pointsDecrease", "radius", "strokeWidth", "normalizedRadius", "circumference", "Math", "PI", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "strokeDashoffset", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "height", "width", "stroke", "fill", "r", "cx", "cy", "style", "strokeLinecap", "toLocaleString", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/PerformanceChart.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\nconst PerformanceChart: React.FC = () => {\n  const [animatedProgress, setAnimatedProgress] = useState(0);\n  const performanceData = {\n    points: 8966,\n    progress: 75, // percentage\n    pointsProgress: 'Point Progress',\n    pointsDecrease: 'Point Decrease'\n  };\n\n  const radius = 60;\n  const strokeWidth = 8;\n  const normalizedRadius = radius - strokeWidth * 2;\n  const circumference = normalizedRadius * 2 * Math.PI;\n  const strokeDasharray = `${circumference} ${circumference}`;\n  const strokeDashoffset = circumference - (performanceData.progress / 100) * circumference;\n\n  return (\n    <div className=\"text-center\">\n      {/* Legend */}\n      <div className=\"flex items-center justify-center space-x-4 mb-6\">\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-orange-400 rounded-full\"></div>\n          <span className=\"text-xs text-gray-600\">{performanceData.pointsProgress}</span>\n        </div>\n        <div className=\"flex items-center space-x-2\">\n          <div className=\"w-3 h-3 bg-gray-300 rounded-full\"></div>\n          <span className=\"text-xs text-gray-600\">{performanceData.pointsDecrease}</span>\n        </div>\n      </div>\n\n      {/* Circular Progress Chart */}\n      <div className=\"relative inline-flex items-center justify-center\">\n        <svg\n          height={radius * 2}\n          width={radius * 2}\n          className=\"transform -rotate-90\"\n        >\n          {/* Background circle */}\n          <circle\n            stroke=\"#e5e7eb\"\n            fill=\"transparent\"\n            strokeWidth={strokeWidth}\n            r={normalizedRadius}\n            cx={radius}\n            cy={radius}\n          />\n          {/* Progress circle */}\n          <circle\n            stroke=\"#fb923c\"\n            fill=\"transparent\"\n            strokeWidth={strokeWidth}\n            strokeDasharray={strokeDasharray}\n            style={{ strokeDashoffset }}\n            strokeLinecap=\"round\"\n            r={normalizedRadius}\n            cx={radius}\n            cy={radius}\n            className=\"transition-all duration-300 ease-in-out\"\n          />\n        </svg>\n\n        {/* Center content */}\n        <div className=\"absolute inset-0 flex flex-col items-center justify-center\">\n          <div className=\"text-2xl font-bold text-gray-900\">{performanceData.points.toLocaleString()}</div>\n          <div className=\"text-xs text-gray-500\">Your Point</div>\n        </div>\n      </div>\n\n      {/* Performance indicators */}\n      <div className=\"mt-6 flex items-center justify-center space-x-8\">\n        {/* Left avatar */}\n        <div className=\"flex flex-col items-center\">\n          <div className=\"w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-2\">\n            <span className=\"text-blue-600 text-lg\">👨‍💼</span>\n          </div>\n        </div>\n\n        {/* Center indicator */}\n        <div className=\"flex flex-col items-center\">\n          <div className=\"w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2 relative\">\n            <span className=\"text-orange-600 text-xl\">⭐</span>\n            <div className=\"absolute -top-1 -right-1 w-4 h-4 bg-orange-400 rounded-full\"></div>\n          </div>\n        </div>\n\n        {/* Right avatar */}\n        <div className=\"flex flex-col items-center\">\n          <div className=\"w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mb-2\">\n            <span className=\"text-gray-600 text-lg\">👩‍💼</span>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default PerformanceChart;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAmB,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEnD,MAAMC,gBAA0B,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACvC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGN,QAAQ,CAAC,CAAC,CAAC;EAC3D,MAAMO,eAAe,GAAG;IACtBC,MAAM,EAAE,IAAI;IACZC,QAAQ,EAAE,EAAE;IAAE;IACdC,cAAc,EAAE,gBAAgB;IAChCC,cAAc,EAAE;EAClB,CAAC;EAED,MAAMC,MAAM,GAAG,EAAE;EACjB,MAAMC,WAAW,GAAG,CAAC;EACrB,MAAMC,gBAAgB,GAAGF,MAAM,GAAGC,WAAW,GAAG,CAAC;EACjD,MAAME,aAAa,GAAGD,gBAAgB,GAAG,CAAC,GAAGE,IAAI,CAACC,EAAE;EACpD,MAAMC,eAAe,GAAG,GAAGH,aAAa,IAAIA,aAAa,EAAE;EAC3D,MAAMI,gBAAgB,GAAGJ,aAAa,GAAIR,eAAe,CAACE,QAAQ,GAAG,GAAG,GAAIM,aAAa;EAEzF,oBACEb,OAAA;IAAKkB,SAAS,EAAC,aAAa;IAAAC,QAAA,gBAE1BnB,OAAA;MAAKkB,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAC9DnB,OAAA;QAAKkB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CnB,OAAA;UAAKkB,SAAS,EAAC;QAAoC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC1DvB,OAAA;UAAMkB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAEd,eAAe,CAACG;QAAc;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC,eACNvB,OAAA;QAAKkB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CnB,OAAA;UAAKkB,SAAS,EAAC;QAAkC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxDvB,OAAA;UAAMkB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAEd,eAAe,CAACI;QAAc;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC5E,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,kDAAkD;MAAAC,QAAA,gBAC/DnB,OAAA;QACEwB,MAAM,EAAEd,MAAM,GAAG,CAAE;QACnBe,KAAK,EAAEf,MAAM,GAAG,CAAE;QAClBQ,SAAS,EAAC,sBAAsB;QAAAC,QAAA,gBAGhCnB,OAAA;UACE0B,MAAM,EAAC,SAAS;UAChBC,IAAI,EAAC,aAAa;UAClBhB,WAAW,EAAEA,WAAY;UACzBiB,CAAC,EAAEhB,gBAAiB;UACpBiB,EAAE,EAAEnB,MAAO;UACXoB,EAAE,EAAEpB;QAAO;UAAAU,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACZ,CAAC,eAEFvB,OAAA;UACE0B,MAAM,EAAC,SAAS;UAChBC,IAAI,EAAC,aAAa;UAClBhB,WAAW,EAAEA,WAAY;UACzBK,eAAe,EAAEA,eAAgB;UACjCe,KAAK,EAAE;YAAEd;UAAiB,CAAE;UAC5Be,aAAa,EAAC,OAAO;UACrBJ,CAAC,EAAEhB,gBAAiB;UACpBiB,EAAE,EAAEnB,MAAO;UACXoB,EAAE,EAAEpB,MAAO;UACXQ,SAAS,EAAC;QAAyC;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACpD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACC,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,4DAA4D;QAAAC,QAAA,gBACzEnB,OAAA;UAAKkB,SAAS,EAAC,kCAAkC;UAAAC,QAAA,EAAEd,eAAe,CAACC,MAAM,CAAC2B,cAAc,CAAC;QAAC;UAAAb,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACjGvB,OAAA;UAAKkB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAU;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAK,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNvB,OAAA;MAAKkB,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAE9DnB,OAAA;QAAKkB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCnB,OAAA;UAAKkB,SAAS,EAAC,0EAA0E;UAAAC,QAAA,eACvFnB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCnB,OAAA;UAAKkB,SAAS,EAAC,qFAAqF;UAAAC,QAAA,gBAClGnB,OAAA;YAAMkB,SAAS,EAAC,yBAAyB;YAAAC,QAAA,EAAC;UAAC;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC,eAClDvB,OAAA;YAAKkB,SAAS,EAAC;UAA6D;YAAAE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChF;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNvB,OAAA;QAAKkB,SAAS,EAAC,4BAA4B;QAAAC,QAAA,eACzCnB,OAAA;UAAKkB,SAAS,EAAC,0EAA0E;UAAAC,QAAA,eACvFnB,OAAA;YAAMkB,SAAS,EAAC,uBAAuB;YAAAC,QAAA,EAAC;UAAK;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjD;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACrB,EAAA,CA9FID,gBAA0B;AAAAiC,EAAA,GAA1BjC,gBAA0B;AAgGhC,eAAeA,gBAAgB;AAAC,IAAAiC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}