import React, { useState } from "react";

// Type definitions
interface Instructor {
  name: string;
  avatar: string;
  stats: string;
  recommend: string;
}

interface CourseItem {
  type: "doc" | "video" | "quiz" | "module";
  label: string;
  duration: string;
}

interface CourseWeek {
  title: string;
  progress: number;
  items: CourseItem[];
}

interface CourseContent {
  sections: number;
  lectures: number;
  hours: number;
  weeks: CourseWeek[];
}

interface Course {
  title: string;
  rating: number;
  reviews: number;
  description: string;
  instructor: Instructor;
  price: number;
  oldPrice: number;
  sale: number;
  image: string;
  includes: string[];
  trial: boolean;
  content: CourseContent;
}

const mockCourseEn: Course = {
  title: "Beginner's Guide to Successful Company Management: Business and User Goals",
  rating: 4.9,
  reviews: 236,
  description:
    "Hello Student! 👋 Are you ready to embark on a comprehensive journey into the realm of successful company management with our Beginner's Guide course? During this course I'll help you delve deep into the intricacies of business strategy, organizational dynamics, and user-centric approaches. Ready to join? Send me a message and let's start! 🚀",
  instructor: {
    name: "<PERSON>",
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
    stats: "250+ students bought this course",
    recommend: "98% students recommend this course",
  },
  price: 87.99,
  oldPrice: 183.0,
  sale: 35,
  image: "https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=400&q=80",
  includes: [
    "65 hours on demand video",
    "45 downloadable resources",
    "Access on mobile and TV",
    "86 articles",
    "30 min personal weekly session",
    "Meeting with Oxford Professor",
    "Certificate of completion",
  ],
  trial: true,
  content: {
    sections: 24,
    lectures: 490,
    hours: 72,
    weeks: [
      {
        title: "Week 1 - Beginner - Introduction to Business Management",
        progress: 22,
        items: [
          { type: "doc", label: "Read before you start", duration: "4min" },
          { type: "video", label: "Introduction to Business Foundations & Principals of Management", duration: "1h 10min" },
          { type: "video", label: "Introduction to Brand Management: Aligning Business, Brand and Behaviour", duration: "1h 23min" },
          { type: "video", label: "Business Analysis & Process Management", duration: "43min" },
          { type: "doc", label: "Major terms from the section", duration: "5 min" },
          { type: "quiz", label: "Practice analyse", duration: "1 Question" },
          { type: "module", label: "Module 1", duration: "24 Questions" },
        ],
      },
      {
        title: "Week 2 - Intermediate - Strategic Planning & Leadership",
        progress: 0,
        items: [
          { type: "video", label: "Strategic Planning Fundamentals", duration: "1h 15min" },
          { type: "video", label: "Leadership Styles and Techniques", duration: "58min" },
          { type: "doc", label: "Strategic Planning Template", duration: "8min" },
          { type: "quiz", label: "Leadership Assessment", duration: "5 Questions" },
          { type: "video", label: "Case Study: Successful Business Transformations", duration: "45min" },
          { type: "module", label: "Module 2", duration: "18 Questions" },
        ],
      },
      {
        title: "Week 3 - Advanced - Financial Management & Analytics",
        progress: 0,
        items: [
          { type: "video", label: "Financial Planning and Budgeting", duration: "1h 30min" },
          { type: "video", label: "Business Analytics and KPIs", duration: "1h 5min" },
          { type: "doc", label: "Financial Templates and Tools", duration: "12min" },
          { type: "quiz", label: "Financial Analysis Quiz", duration: "8 Questions" },
          { type: "video", label: "ROI and Performance Metrics", duration: "52min" },
          { type: "module", label: "Final Assessment", duration: "35 Questions" },
        ],
      },
    ],
  },
};

const mockCourseAr: Course = {
  title: "دليل المبتدئين لإدارة الشركات الناجحة: أهداف العمل والمستخدم",
  rating: 4.9,
  reviews: 236,
  description:
    "مرحبًا أيها الطالب! 👋 هل أنت مستعد لبدء رحلة شاملة في عالم إدارة الشركات الناجحة مع دليل المبتدئين الخاص بنا؟ خلال هذه الدورة سأساعدك على التعمق في استراتيجيات الأعمال وديناميكيات التنظيم ونهج التركيز على المستخدم. مستعد للانضمام؟ أرسل لي رسالة ولنبدأ! 🚀",
  instructor: {
    name: "جيني ويلسون",
    avatar: "https://randomuser.me/api/portraits/women/44.jpg",
    stats: "أكثر من 250 طالب اشتروا هذه الدورة",
    recommend: "98% من الطلاب يوصون بهذه الدورة",
  },
  price: 87.99,
  oldPrice: 183.0,
  sale: 35,
  image: "https://images.unsplash.com/photo-1513258496099-48168024aec0?auto=format&fit=crop&w=400&q=80",
  includes: [
    "65 ساعة فيديو عند الطلب",
    "45 موردًا قابلًا للتنزيل",
    "الوصول عبر الجوال والتلفاز",
    "86 مقالة",
    "جلسة أسبوعية شخصية لمدة 30 دقيقة",
    "اجتماع مع أستاذ من أكسفورد",
    "شهادة إتمام",
  ],
  trial: true,
  content: {
    sections: 24,
    lectures: 490,
    hours: 72,
    weeks: [
      {
        title: "الأسبوع 1 - المبتدئ - مقدمة في إدارة الأعمال",
        progress: 22,
        items: [
          { type: "doc", label: "اقرأ قبل أن تبدأ", duration: "4 دقائق" },
          { type: "video", label: "مقدمة في أسس الأعمال ومبادئ الإدارة", duration: "1س 10د" },
          { type: "video", label: "مقدمة في إدارة العلامة التجارية: مواءمة الأعمال والعلامة والسلوك", duration: "1س 23د" },
          { type: "video", label: "تحليل الأعمال وإدارة العمليات", duration: "43 دقيقة" },
          { type: "doc", label: "المصطلحات الرئيسية من القسم", duration: "5 دقائق" },
          { type: "quiz", label: "تحليل عملي", duration: "سؤال واحد" },
          { type: "module", label: "الوحدة 1", duration: "24 سؤالاً" },
        ],
      },
    ],
  },
};

const iconMap: Record<string, any> = {
  video: (
    <svg className="w-5 h-5 text-blue-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M4 6h8a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2z" /></svg>
  ),
  doc: (
    <svg className="w-5 h-5 text-green-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M7 7h10M7 11h10M7 15h6" /></svg>
  ),
  quiz: (
    <svg className="w-5 h-5 text-yellow-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><path strokeLinecap="round" strokeLinejoin="round" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
  ),
  module: (
    <svg className="w-5 h-5 text-purple-500" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24"><circle cx="12" cy="12" r="10" /><path strokeLinecap="round" strokeLinejoin="round" d="M8 12h8" /></svg>
  ),
};

// Sidebar Navigation Component
const SidebarNav = () => {
  const navItems = [
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
        </svg>
      ),
      active: true
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
      active: false
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
        </svg>
      ),
      active: false
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
      active: false
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
      ),
      active: false
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
        </svg>
      ),
      active: false
    },
    {
      icon: (
        <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
        </svg>
      ),
      active: false
    },
  ];

  return (
    <div className="hidden lg:flex fixed left-0 top-0 h-full w-16 bg-gray-900 flex-col items-center py-4 z-10">
      <div className="w-10 h-10 bg-white rounded-lg flex items-center justify-center mb-8">
        <svg className="w-6 h-6 text-gray-900" fill="currentColor" viewBox="0 0 24 24">
          <path d="M13.5 2c-5.621 0-10.211 4.443-10.475 10h-3.025l5 6.625 5-6.625h-2.975c.257-3.351 3.06-6 6.475-6 3.584 0 6.5 2.916 6.5 6.5s-2.916 6.5-6.5 6.5c-1.863 0-3.542-.793-4.728-2.053l-2.427 3.216c1.877 1.754 4.389 2.837 7.155 2.837 5.79 0 10.5-4.71 10.5-10.5s-4.71-10.5-10.5-10.5z"/>
        </svg>
      </div>
      {navItems.map((item, index) => (
        <div
          key={index}
          className={`w-10 h-10 rounded-lg flex items-center justify-center mb-2 cursor-pointer transition-colors ${
            item.active ? 'bg-purple-600 text-white' : 'text-gray-400 hover:bg-gray-800'
          }`}
        >
          {item.icon}
        </div>
      ))}
    </div>
  );
};

function CourseDetail() {
  const [lang, setLang] = useState("en");
  const [expandedWeeks, setExpandedWeeks] = useState<number[]>([0]); // First week expanded by default
  const [showPreview, setShowPreview] = useState(false);
  const [completedItems, setCompletedItems] = useState<string[]>([]);
  const course = lang === "ar" ? mockCourseAr : mockCourseEn;
  const dir = lang === "ar" ? "rtl" : "ltr";

  const toggleWeek = (weekIndex: number) => {
    setExpandedWeeks(prev =>
      prev.includes(weekIndex)
        ? prev.filter(i => i !== weekIndex)
        : [...prev, weekIndex]
    );
  };

  const toggleItemCompletion = (itemId: string) => {
    setCompletedItems(prev =>
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  // Text translations
  const t = {
    en: {
      courses: "Courses",
      popular: "Popular courses",
      basedOn: "based on",
      reviews: "reviews",
      courseContent: "Course content",
      sections: "sections",
      lectures: "lectures",
      hours: "hours total length",
      expand: "Expand all sections",
      buy: "Buy course now",
      message: "Send message to teacher",
      includes: "This course includes",
      trial: "10 min trial course",
      trialDesc: "Have a look and feel at the course with a quick trial.",
      preview: "Preview",
    },
    ar: {
      courses: "الدورات",
      popular: "الدورات الشائعة",
      basedOn: "بناءً على",
      reviews: "مراجعة",
      courseContent: "محتوى الدورة",
      sections: "أقسام",
      lectures: "محاضرات",
      hours: "إجمالي الساعات",
      expand: "توسيع جميع الأقسام",
      buy: "اشترِ الدورة الآن",
      message: "أرسل رسالة للمدرس",
      includes: "تشمل هذه الدورة",
      trial: "دورة تجريبية لمدة 10 دقائق",
      trialDesc: "اطلع على الدورة بسرعة.",
      preview: "معاينة",
    },
  };

  return (
    <div className="min-h-screen bg-gray-50" dir={dir}>
      <SidebarNav />

      {/* Main Content with responsive margin */}
      <div className="lg:ml-16">
        {/* Header */}
        <div className="bg-white border-b px-4 py-3 sm:px-6 sm:py-4">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 sm:flex-initial">
                <input
                  className="pl-10 pr-4 py-2 border border-gray-200 rounded-lg w-full sm:w-80 text-sm focus:outline-none focus:ring-2 focus:ring-purple-500"
                  placeholder={lang === 'ar' ? 'بحث' : 'Search'}
                />
                <svg className="w-4 h-4 text-gray-400 absolute left-3 top-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            <div className="flex items-center gap-3 sm:gap-4">
              <button className="p-2 text-gray-400 hover:text-gray-600 hidden sm:block">
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-5 5v-5zM9 7H4l5-5v5z" />
                </svg>
              </button>
              <img src={course.instructor.avatar} alt="User" className="w-8 h-8 rounded-full" />
              <button
                className="px-3 py-1 rounded bg-purple-600 text-white text-xs font-semibold hover:bg-purple-700 transition"
                onClick={() => setLang(lang === "en" ? "ar" : "en")}
              >
                {lang === "en" ? "العربية" : "English"}
              </button>
            </div>
          </div>
        </div>

        {/* Breadcrumb */}
        <div className="px-4 py-3 sm:px-6 sm:py-4">
          <div className="text-sm text-gray-500 flex items-center gap-2 overflow-x-auto">
            <span className="whitespace-nowrap">{t[lang].courses}</span>
            <span>/</span>
            <span className="whitespace-nowrap">{t[lang].popular}</span>
            <span>/</span>
            <span className="font-medium text-gray-700 truncate">{course.title}</span>
          </div>
        </div>
        {/* Main Content */}
        <div className="px-4 pb-4 sm:px-6 sm:pb-6">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
            {/* Left/Main Content */}
            <div className="lg:col-span-2 order-2 lg:order-1">
              {/* Course Header */}
              <div className="mb-4 sm:mb-6">
                <div className="flex flex-col sm:flex-row sm:items-center gap-2 text-yellow-500 text-sm font-medium mb-3">
                  <div className="flex items-center gap-2">
                    <span>★ {course.rating}</span>
                    <span className="text-gray-500 text-sm font-normal">{t[lang].basedOn} {course.reviews} {t[lang].reviews}</span>
                  </div>
                  <button className="sm:ml-auto bg-gray-100 hover:bg-gray-200 rounded-full p-2 text-gray-500 self-start sm:self-center">
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                    </svg>
                  </button>
                </div>
                <h1 className="text-2xl sm:text-3xl font-bold mb-4 text-gray-900 leading-tight">
                  {course.title}
                </h1>
                <p className="text-gray-600 text-sm sm:text-base mb-4 sm:mb-6 leading-relaxed">{course.description}</p>
                <div className="flex items-center gap-3">
                  <img src={course.instructor.avatar} alt="Instructor" className="w-10 h-10 rounded-full" />
                  <div>
                    <div className="font-medium text-gray-900 text-sm">{course.instructor.name}</div>
                    <div className="flex flex-col sm:flex-row sm:items-center gap-1 sm:gap-4 text-xs text-gray-500 mt-1">
                      <span>{course.instructor.stats}</span>
                      <span className="hidden sm:inline">•</span>
                      <span>{course.instructor.recommend}</span>
                    </div>
                  </div>
                </div>
              </div>
              {/* Course Content */}
              <div className="bg-white rounded-lg border border-gray-200 p-4 sm:p-6">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4 sm:mb-6">
                  <h2 className="text-lg font-semibold text-gray-900">{t[lang].courseContent}</h2>
                  <button className="text-purple-600 text-sm font-medium hover:underline self-start sm:self-center">{t[lang].expand}</button>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-3 gap-3 sm:gap-6 mb-4 sm:mb-6 text-sm text-gray-600">
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <span>{course.content.sections} {t[lang].sections}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M4 6h8a2 2 0 012 2v8a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2z" />
                    </svg>
                    <span>{course.content.lectures} {t[lang].lectures}</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <svg className="w-4 h-4 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span>{course.content.hours} {t[lang].hours}</span>
                  </div>
                </div>

                {/* Week Content */}
                {course.content.weeks.map((week, i) => {
                  const isExpanded = expandedWeeks.includes(i);
                  const completedCount = week.items.filter(item =>
                    completedItems.includes(`${i}-${item.label}`)
                  ).length;
                  const progressPercent = (completedCount / week.items.length) * 100;

                  return (
                    <div key={i} className="border border-gray-200 rounded-lg mb-4 overflow-hidden">
                      <button
                        onClick={() => toggleWeek(i)}
                        className="w-full flex items-center justify-between p-4 bg-gray-50 hover:bg-gray-100 transition-colors"
                      >
                        <div className="flex items-center gap-3">
                          <svg
                            className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${
                              isExpanded ? 'rotate-180' : ''
                            }`}
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7" />
                          </svg>
                          <span className="font-medium text-gray-900">{week.title}</span>
                          <div className="flex items-center gap-2">
                            <span className="text-xs text-purple-600 bg-purple-100 rounded-full px-2 py-1">
                              {Math.round(progressPercent)}%
                            </span>
                            <span className="text-xs text-gray-500">
                              {completedCount}/{week.items.length} completed
                            </span>
                          </div>
                        </div>
                        <div className="w-16 h-2 bg-gray-200 rounded-full overflow-hidden">
                          <div
                            className="h-full bg-purple-600 transition-all duration-300"
                            style={{ width: `${progressPercent}%` }}
                          />
                        </div>
                      </button>

                      <div className={`transition-all duration-300 ease-in-out ${
                        isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0'
                      } overflow-hidden`}>
                        <div className="p-4">
                          {week.items.map((item, j) => {
                            const itemId = `${i}-${item.label}`;
                            const isCompleted = completedItems.includes(itemId);

                            return (
                              <div
                                key={j}
                                className="flex items-center gap-3 py-3 border-b border-gray-100 last:border-b-0 hover:bg-gray-50 rounded px-2 transition-colors group"
                              >
                                <button
                                  onClick={() => toggleItemCompletion(itemId)}
                                  className={`w-5 h-5 rounded-full border-2 flex items-center justify-center transition-colors ${
                                    isCompleted
                                      ? 'bg-green-500 border-green-500 text-white'
                                      : 'border-gray-300 hover:border-green-400'
                                  }`}
                                >
                                  {isCompleted && (
                                    <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                    </svg>
                                  )}
                                </button>
                                {iconMap[item.type]}
                                <span className={`text-sm flex-1 transition-colors ${
                                  isCompleted ? 'text-gray-500 line-through' : 'text-gray-700'
                                }`}>
                                  {item.label}
                                </span>
                                <span className="text-xs text-gray-500">{item.duration}</span>
                                <button className="opacity-0 group-hover:opacity-100 transition-opacity p-1 hover:bg-gray-200 rounded">
                                  <svg className="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M16 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                  </svg>
                                </button>
                              </div>
                            );
                          })}
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </div>
            {/* Sidebar */}
            <aside className="lg:col-span-1 order-1 lg:order-2">
              <div className="bg-white rounded-lg border border-gray-200 overflow-hidden lg:sticky lg:top-6">
                {/* Course Image */}
                <div className="relative group">
                  <img src={course.image} alt="Course" className="w-full h-48 sm:h-56 lg:h-48 object-cover" />
                  <div className="absolute inset-0 bg-black/20 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center">
                    <button
                      onClick={() => setShowPreview(true)}
                      className="bg-white/90 backdrop-blur-sm rounded-full p-3 text-gray-800 hover:bg-white transition transform hover:scale-110"
                    >
                      <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M16 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                      </svg>
                    </button>
                  </div>
                  <div className="absolute top-4 right-4">
                    <button className="bg-white/90 backdrop-blur-sm rounded-full p-2 text-gray-600 hover:bg-white transition transform hover:scale-110">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                      </svg>
                    </button>
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <span className="bg-black/70 text-white text-xs px-2 py-1 rounded">
                      Preview Available
                    </span>
                  </div>
                </div>

                {/* Course Info */}
                <div className="p-4 sm:p-6">
                  <div className="flex flex-wrap items-center gap-2 sm:gap-3 mb-4">
                    <span className="text-2xl sm:text-3xl font-bold text-gray-900">${course.price.toFixed(2)}</span>
                    <span className="text-base sm:text-lg line-through text-gray-400">${course.oldPrice.toFixed(2)}</span>
                    <span className="text-xs bg-orange-100 text-orange-600 rounded-full px-2 py-1 font-medium">{course.sale}% sale</span>
                  </div>

                  <div className="space-y-3 mb-6">
                    <button className="w-full bg-purple-600 hover:bg-purple-700 text-white font-semibold py-3 rounded-lg transition flex items-center justify-center gap-2 text-sm sm:text-base">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
                      </svg>
                      {t[lang].buy}
                    </button>
                    <button className="w-full border border-gray-200 hover:bg-gray-50 text-gray-700 font-medium py-3 rounded-lg transition flex items-center justify-center gap-2 text-sm sm:text-base">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
                      </svg>
                      {t[lang].message}
                    </button>
                  </div>

                  <div className="mb-6">
                    <h3 className="font-semibold text-gray-900 text-sm mb-3">{t[lang].includes}</h3>
                    <ul className="space-y-2">
                      {course.includes.map((inc, i) => (
                        <li key={i} className="flex items-start gap-3 text-sm text-gray-600">
                          <svg className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M5 13l4 4L19 7" />
                          </svg>
                          <span className="leading-relaxed">{inc}</span>
                        </li>
                      ))}
                    </ul>
                  </div>

                  {course.trial && (
                    <div className="bg-gray-900 rounded-lg p-4 text-center">
                      <h4 className="font-semibold text-white text-sm mb-2">{t[lang].trial}</h4>
                      <p className="text-gray-300 text-xs mb-3">{t[lang].trialDesc}</p>
                      <button
                        onClick={() => setShowPreview(true)}
                        className="w-full bg-white text-gray-900 rounded-lg py-2 text-sm font-medium hover:bg-gray-100 transition flex items-center justify-center gap-2"
                      >
                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M16 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        {t[lang].preview}
                      </button>
                    </div>
                  )}
                </div>
              </div>
            </aside>
          </div>
        </div>

        {/* Preview Modal */}
        {showPreview && (
          <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
            <div className="bg-white rounded-lg max-w-4xl w-full max-h-[90vh] overflow-hidden">
              <div className="flex items-center justify-between p-4 border-b">
                <h3 className="text-lg font-semibold">Course Preview</h3>
                <button
                  onClick={() => setShowPreview(false)}
                  className="p-2 hover:bg-gray-100 rounded-full transition"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                  </svg>
                </button>
              </div>
              <div className="p-6">
                <div className="aspect-video bg-gray-100 rounded-lg flex items-center justify-center mb-4">
                  <div className="text-center">
                    <svg className="w-16 h-16 text-gray-400 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M14.828 14.828a4 4 0 01-5.656 0M9 10h1m4 0h1m-6 4h.01M16 14h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <p className="text-gray-500">Course Preview Video</p>
                    <p className="text-sm text-gray-400 mt-1">10 minute sample from the course</p>
                  </div>
                </div>
                <div className="text-center">
                  <button
                    onClick={() => setShowPreview(false)}
                    className="bg-purple-600 text-white px-6 py-2 rounded-lg hover:bg-purple-700 transition"
                  >
                    Start Learning Now
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

export default CourseDetail;