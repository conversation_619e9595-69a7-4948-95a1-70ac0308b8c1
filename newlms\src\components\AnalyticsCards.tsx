import React from 'react';

interface AnalyticsCardProps {
  title: string;
  value: string;
  subtitle: string;
  icon: string;
  color: string;
  bgColor: string;
}

const AnalyticsCard: React.FC<AnalyticsCardProps> = ({ title, value, subtitle, icon, color, bgColor }) => {
  return (
    <div className="bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-300 cursor-pointer group">
      <div className="flex items-center justify-between mb-4">
        <div className={`w-12 h-12 ${bgColor} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>
          <span className={`text-xl ${color}`}>{icon}</span>
        </div>
        <div className="text-right">
          <div className="text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300">{value}</div>
          <div className="text-sm text-gray-500">{subtitle}</div>
        </div>
      </div>
      <h3 className="text-sm font-medium text-gray-600 group-hover:text-gray-800 transition-colors duration-300">{title}</h3>
    </div>
  );
};

const AnalyticsCards: React.FC = () => {
  const analyticsData = [
    {
      title: 'Weekly Analysis',
      value: '02',
      subtitle: '5 Min 10 Sec',
      icon: '📊',
      color: 'text-blue-600',
      bgColor: 'bg-blue-100'
    },
    {
      title: 'Total Course',
      value: '04',
      subtitle: '5 Min 7 Sec',
      icon: '📚',
      color: 'text-orange-600',
      bgColor: 'bg-orange-100'
    },
    {
      title: 'Course Enroll',
      value: '3.69k',
      subtitle: '5 Min 7 Sec',
      icon: '👥',
      color: 'text-purple-600',
      bgColor: 'bg-purple-100'
    }
  ];

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {analyticsData.map((item, index) => (
        <div key={index} className="animate-slide-up" style={{ animationDelay: `${index * 0.1}s` }}>
          <AnalyticsCard {...item} />
        </div>
      ))}
    </div>
  );
};

export default AnalyticsCards;
