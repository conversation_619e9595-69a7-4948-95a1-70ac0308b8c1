{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\Dashboard.tsx\";\nimport React from 'react';\nimport Sidebar from './components/Sidebar.tsx';\nimport Header from './components/Header.tsx';\nimport WelcomeSection from './components/WelcomeSection.tsx';\nimport AnalyticsCards from './components/AnalyticsCards.tsx';\nimport FeaturedCourses from './components/FeaturedCourses.tsx';\nimport RightSidebar from './components/RightSidebar.tsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"min-h-screen bg-gray-50 flex\",\n    children: [/*#__PURE__*/_jsxDEV(Sidebar, {}, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 flex flex-col\",\n      children: [/*#__PURE__*/_jsxDEV(Header, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 18,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 p-6 space-y-6\",\n          children: [/*#__PURE__*/_jsxDEV(WelcomeSection, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 25,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(AnalyticsCards, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 28,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(FeaturedCourses, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 31,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(RightSidebar, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 35,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 21,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 16,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 11,\n    columnNumber: 5\n  }, this);\n};\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "Sidebar", "Header", "WelcomeSection", "AnalyticsCards", "FeaturedCourses", "RightSidebar", "jsxDEV", "_jsxDEV", "Dashboard", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/Dashboard.tsx"], "sourcesContent": ["import React from 'react';\nimport Sidebar from './components/Sidebar.tsx';\nimport Header from './components/Header.tsx';\nimport WelcomeSection from './components/WelcomeSection.tsx';\nimport AnalyticsCards from './components/AnalyticsCards.tsx';\nimport FeaturedCourses from './components/FeaturedCourses.tsx';\nimport RightSidebar from './components/RightSidebar.tsx';\n\nconst Dashboard: React.FC = () => {\n  return (\n    <div className=\"min-h-screen bg-gray-50 flex\">\n      {/* Left Sidebar */}\n      <Sidebar />\n\n      {/* Main Content */}\n      <div className=\"flex-1 flex flex-col\">\n        {/* Header */}\n        <Header />\n\n        {/* Main Dashboard Content */}\n        <div className=\"flex-1 flex\">\n          {/* Center Content */}\n          <div className=\"flex-1 p-6 space-y-6\">\n            {/* Welcome Section */}\n            <WelcomeSection />\n\n            {/* Analytics Cards */}\n            <AnalyticsCards />\n\n            {/* Featured Courses */}\n            <FeaturedCourses />\n          </div>\n\n          {/* Right Sidebar */}\n          <RightSidebar />\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,YAAY,MAAM,+BAA+B;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEzD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAChC,oBACED,OAAA;IAAKE,SAAS,EAAC,8BAA8B;IAAAC,QAAA,gBAE3CH,OAAA,CAACP,OAAO;MAAAW,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAE,CAAC,eAGXP,OAAA;MAAKE,SAAS,EAAC,sBAAsB;MAAAC,QAAA,gBAEnCH,OAAA,CAACN,MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAGVP,OAAA;QAAKE,SAAS,EAAC,aAAa;QAAAC,QAAA,gBAE1BH,OAAA;UAAKE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,gBAEnCH,OAAA,CAACL,cAAc;YAAAS,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGlBP,OAAA,CAACJ,cAAc;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,eAGlBP,OAAA,CAACH,eAAe;YAAAO,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB,CAAC,eAGNP,OAAA,CAACF,YAAY;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACb,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GA/BIP,SAAmB;AAiCzB,eAAeA,SAAS;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}