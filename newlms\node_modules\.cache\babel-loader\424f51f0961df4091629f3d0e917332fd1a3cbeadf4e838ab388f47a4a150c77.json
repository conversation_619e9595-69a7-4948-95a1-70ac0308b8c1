{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\FeaturedCourses.tsx\",\n  _s = $RefreshSig$();\nimport React from 'react';\nimport { useToast } from '../hooks/useToast.tsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst FeaturedCourses = () => {\n  _s();\n  const [savedCourses, setSavedCourses] = React.useState([2]);\n  const [searchTerm, setSearchTerm] = React.useState('');\n  const [sortBy, setSortBy] = React.useState('name');\n  const {\n    showSuccess,\n    showInfo\n  } = useToast();\n  const toggleSaved = (courseId, courseName) => {\n    const isCurrentlySaved = savedCourses.includes(courseId);\n    setSavedCourses(prev => prev.includes(courseId) ? prev.filter(id => id !== courseId) : [...prev, courseId]);\n\n    // Show toast notification\n    if (isCurrentlySaved) {\n      showInfo(`Removed \"${courseName}\" from saved courses`);\n    } else {\n      showSuccess(`Added \"${courseName}\" to saved courses`);\n    }\n  };\n  const courses = [{\n    id: 1,\n    name: 'Basics of Mobile UX',\n    instructor: 'Bruno Scott',\n    instructorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',\n    startDate: 'Feb 12',\n    progress: '15/20',\n    type: 'UI DESIGN',\n    category: 'Design',\n    saved: false\n  }, {\n    id: 2,\n    name: 'Digital Design System',\n    instructor: 'Bruno Scott',\n    instructorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',\n    startDate: 'Feb 14',\n    progress: '07/02',\n    type: 'UI DESIGN',\n    category: 'Design',\n    saved: true\n  }];\n\n  // Filter and sort courses\n  const filteredCourses = courses.filter(course => course.name.toLowerCase().includes(searchTerm.toLowerCase()) || course.instructor.toLowerCase().includes(searchTerm.toLowerCase())).sort((a, b) => {\n    switch (sortBy) {\n      case 'name':\n        return a.name.localeCompare(b.name);\n      case 'date':\n        return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();\n      case 'progress':\n        return parseInt(a.progress.split('/')[0]) - parseInt(b.progress.split('/')[0]);\n      default:\n        return 0;\n    }\n  });\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-xl border border-gray-200\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 border-b border-gray-200\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between mb-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Featured Courses\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"text-sm text-blue-600 hover:text-blue-700 font-medium\",\n          children: \"View all \\u2192\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 88,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 86,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 max-w-sm\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n              children: /*#__PURE__*/_jsxDEV(\"svg\", {\n                className: \"h-4 w-4 text-gray-400\",\n                fill: \"none\",\n                stroke: \"currentColor\",\n                viewBox: \"0 0 24 24\",\n                children: /*#__PURE__*/_jsxDEV(\"path\", {\n                  strokeLinecap: \"round\",\n                  strokeLinejoin: \"round\",\n                  strokeWidth: 2,\n                  d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 97,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              placeholder: \"Search courses...\",\n              value: searchTerm,\n              onChange: e => setSearchTerm(e.target.value),\n              className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 102,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 96,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 95,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"select\", {\n          value: sortBy,\n          onChange: e => setSortBy(e.target.value),\n          className: \"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\",\n          children: [/*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"name\",\n            children: \"Sort by Name\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 117,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"date\",\n            children: \"Sort by Date\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 118,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"option\", {\n            value: \"progress\",\n            children: \"Sort by Progress\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 119,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 112,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 85,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"overflow-x-auto\",\n      children: /*#__PURE__*/_jsxDEV(\"table\", {\n        className: \"w-full\",\n        children: [/*#__PURE__*/_jsxDEV(\"thead\", {\n          className: \"bg-gray-50\",\n          children: /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: [/*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Course name\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Start\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Course\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 135,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Type\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 138,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"th\", {\n              className: \"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\",\n              children: \"Save\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 141,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"tbody\", {\n          className: \"bg-white divide-y divide-gray-200\",\n          children: filteredCourses.length === 0 ? /*#__PURE__*/_jsxDEV(\"tr\", {\n            children: /*#__PURE__*/_jsxDEV(\"td\", {\n              colSpan: 5,\n              className: \"px-6 py-8 text-center text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex flex-col items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-12 h-12 text-gray-300 mb-4\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20a7.962 7.962 0 01-6-2.709M3 12a9 9 0 1118 0 9 9 0 01-18 0z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 152,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 151,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                  className: \"text-sm\",\n                  children: \"No courses found matching your search.\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 154,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 150,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 149,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 148,\n            columnNumber: 15\n          }, this) : filteredCourses.map(course => /*#__PURE__*/_jsxDEV(\"tr\", {\n            className: \"hover:bg-gray-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex items-center\",\n                children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex-shrink-0 h-10 w-10\",\n                  children: /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center\",\n                    children: /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"text-blue-600 font-semibold text-sm\",\n                      children: \"\\uD83D\\uDCF1\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 165,\n                      columnNumber: 25\n                    }, this)\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 164,\n                    columnNumber: 23\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 163,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"ml-4\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"text-sm font-medium text-gray-900\",\n                    children: course.name\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 169,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex items-center mt-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"img\", {\n                      className: \"h-5 w-5 rounded-full\",\n                      src: course.instructorAvatar,\n                      alt: course.instructor\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 171,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n                      className: \"ml-2 text-sm text-gray-500\",\n                      children: course.instructor\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 176,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 170,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 168,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 162,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 161,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n              children: course.startDate\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 181,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-900\",\n              children: course.progress\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 184,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\",\n                children: course.type\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"td\", {\n              className: \"px-6 py-4 whitespace-nowrap text-sm text-gray-500\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                onClick: () => toggleSaved(course.id, course.name),\n                className: \"text-gray-400 hover:text-gray-600 transition-colors duration-200\",\n                children: savedCourses.includes(course.id) ? /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 text-yellow-400 hover:text-yellow-500\",\n                  fill: \"currentColor\",\n                  viewBox: \"0 0 20 20\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    d: \"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 199,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 198,\n                  columnNumber: 23\n                }, this) : /*#__PURE__*/_jsxDEV(\"svg\", {\n                  className: \"w-5 h-5 hover:text-yellow-400\",\n                  fill: \"none\",\n                  stroke: \"currentColor\",\n                  viewBox: \"0 0 24 24\",\n                  children: /*#__PURE__*/_jsxDEV(\"path\", {\n                    strokeLinecap: \"round\",\n                    strokeLinejoin: \"round\",\n                    strokeWidth: 2,\n                    d: \"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 203,\n                    columnNumber: 25\n                  }, this)\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 202,\n                  columnNumber: 23\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 193,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 192,\n              columnNumber: 17\n            }, this)]\n          }, course.id, true, {\n            fileName: _jsxFileName,\n            lineNumber: 160,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 146,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 126,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 125,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 83,\n    columnNumber: 5\n  }, this);\n};\n_s(FeaturedCourses, \"yl61AzpCZna/HSjVO7r9its1ZHA=\", false, function () {\n  return [useToast];\n});\n_c = FeaturedCourses;\nexport default FeaturedCourses;\nvar _c;\n$RefreshReg$(_c, \"FeaturedCourses\");", "map": {"version": 3, "names": ["React", "useToast", "jsxDEV", "_jsxDEV", "FeaturedCourses", "_s", "savedCourses", "setSavedCourses", "useState", "searchTerm", "setSearchTerm", "sortBy", "setSortBy", "showSuccess", "showInfo", "toggleSaved", "courseId", "courseName", "isCurrentlySaved", "includes", "prev", "filter", "id", "courses", "name", "instructor", "<PERSON><PERSON><PERSON><PERSON>", "startDate", "progress", "type", "category", "saved", "filteredCourses", "course", "toLowerCase", "sort", "a", "b", "localeCompare", "Date", "getTime", "parseInt", "split", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "placeholder", "value", "onChange", "e", "target", "length", "colSpan", "map", "src", "alt", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/FeaturedCourses.tsx"], "sourcesContent": ["import React, { useMemo, useCallback } from 'react';\nimport { useToast } from '../hooks/useToast.tsx';\n\ninterface Course {\n  id: number;\n  name: string;\n  instructor: string;\n  instructorAvatar: string;\n  startDate: string;\n  progress: string;\n  type: string;\n  category: string;\n  saved: boolean;\n}\n\nconst FeaturedCourses: React.FC = () => {\n  const [savedCourses, setSavedCourses] = React.useState<number[]>([2]);\n  const [searchTerm, setSearchTerm] = React.useState('');\n  const [sortBy, setSortBy] = React.useState<'name' | 'date' | 'progress'>('name');\n  const { showSuccess, showInfo } = useToast();\n\n  const toggleSaved = (courseId: number, courseName: string) => {\n    const isCurrentlySaved = savedCourses.includes(courseId);\n    setSavedCourses(prev =>\n      prev.includes(courseId)\n        ? prev.filter(id => id !== courseId)\n        : [...prev, courseId]\n    );\n\n    // Show toast notification\n    if (isCurrentlySaved) {\n      showInfo(`Removed \"${courseName}\" from saved courses`);\n    } else {\n      showSuccess(`Added \"${courseName}\" to saved courses`);\n    }\n  };\n\n  const courses: Course[] = [\n    {\n      id: 1,\n      name: 'Basics of Mobile UX',\n      instructor: 'Bruno Scott',\n      instructorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',\n      startDate: 'Feb 12',\n      progress: '15/20',\n      type: 'UI DESIGN',\n      category: 'Design',\n      saved: false\n    },\n    {\n      id: 2,\n      name: 'Digital Design System',\n      instructor: 'Bruno Scott',\n      instructorAvatar: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=40&h=40&fit=crop&crop=face',\n      startDate: 'Feb 14',\n      progress: '07/02',\n      type: 'UI DESIGN',\n      category: 'Design',\n      saved: true\n    }\n  ];\n\n  // Filter and sort courses\n  const filteredCourses = courses\n    .filter(course =>\n      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n      course.instructor.toLowerCase().includes(searchTerm.toLowerCase())\n    )\n    .sort((a, b) => {\n      switch (sortBy) {\n        case 'name':\n          return a.name.localeCompare(b.name);\n        case 'date':\n          return new Date(a.startDate).getTime() - new Date(b.startDate).getTime();\n        case 'progress':\n          return parseInt(a.progress.split('/')[0]) - parseInt(b.progress.split('/')[0]);\n        default:\n          return 0;\n      }\n    });\n\n  return (\n    <div className=\"bg-white rounded-xl border border-gray-200\">\n      {/* Header */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between mb-4\">\n          <h2 className=\"text-lg font-semibold text-gray-900\">Featured Courses</h2>\n          <button className=\"text-sm text-blue-600 hover:text-blue-700 font-medium\">\n            View all →\n          </button>\n        </div>\n\n        {/* Search and Filter */}\n        <div className=\"flex items-center space-x-4\">\n          <div className=\"flex-1 max-w-sm\">\n            <div className=\"relative\">\n              <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n                <svg className=\"h-4 w-4 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                  <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n                </svg>\n              </div>\n              <input\n                type=\"text\"\n                placeholder=\"Search courses...\"\n                value={searchTerm}\n                onChange={(e) => setSearchTerm(e.target.value)}\n                className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg text-sm leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n              />\n            </div>\n          </div>\n\n          <select\n            value={sortBy}\n            onChange={(e) => setSortBy(e.target.value as 'name' | 'date' | 'progress')}\n            className=\"border border-gray-300 rounded-lg px-3 py-2 text-sm focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n          >\n            <option value=\"name\">Sort by Name</option>\n            <option value=\"date\">Sort by Date</option>\n            <option value=\"progress\">Sort by Progress</option>\n          </select>\n        </div>\n      </div>\n\n      {/* Table */}\n      <div className=\"overflow-x-auto\">\n        <table className=\"w-full\">\n          <thead className=\"bg-gray-50\">\n            <tr>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Course name\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Start\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Course\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Type\n              </th>\n              <th className=\"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider\">\n                Save\n              </th>\n            </tr>\n          </thead>\n          <tbody className=\"bg-white divide-y divide-gray-200\">\n            {filteredCourses.length === 0 ? (\n              <tr>\n                <td colSpan={5} className=\"px-6 py-8 text-center text-gray-500\">\n                  <div className=\"flex flex-col items-center\">\n                    <svg className=\"w-12 h-12 text-gray-300 mb-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                      <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9.172 16.172a4 4 0 015.656 0M9 12h6m-6-4h6m2 5.291A7.962 7.962 0 0112 20a7.962 7.962 0 01-6-2.709M3 12a9 9 0 1118 0 9 9 0 01-18 0z\" />\n                    </svg>\n                    <p className=\"text-sm\">No courses found matching your search.</p>\n                  </div>\n                </td>\n              </tr>\n            ) : (\n              filteredCourses.map((course) => (\n              <tr key={course.id} className=\"hover:bg-gray-50\">\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <div className=\"flex items-center\">\n                    <div className=\"flex-shrink-0 h-10 w-10\">\n                      <div className=\"h-10 w-10 rounded-lg bg-blue-100 flex items-center justify-center\">\n                        <span className=\"text-blue-600 font-semibold text-sm\">📱</span>\n                      </div>\n                    </div>\n                    <div className=\"ml-4\">\n                      <div className=\"text-sm font-medium text-gray-900\">{course.name}</div>\n                      <div className=\"flex items-center mt-1\">\n                        <img\n                          className=\"h-5 w-5 rounded-full\"\n                          src={course.instructorAvatar}\n                          alt={course.instructor}\n                        />\n                        <span className=\"ml-2 text-sm text-gray-500\">{course.instructor}</span>\n                      </div>\n                    </div>\n                  </div>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {course.startDate}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-900\">\n                  {course.progress}\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap\">\n                  <span className=\"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-blue-100 text-blue-800\">\n                    {course.type}\n                  </span>\n                </td>\n                <td className=\"px-6 py-4 whitespace-nowrap text-sm text-gray-500\">\n                  <button\n                    onClick={() => toggleSaved(course.id, course.name)}\n                    className=\"text-gray-400 hover:text-gray-600 transition-colors duration-200\"\n                  >\n                    {savedCourses.includes(course.id) ? (\n                      <svg className=\"w-5 h-5 text-yellow-400 hover:text-yellow-500\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n                        <path d=\"M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z\" />\n                      </svg>\n                    ) : (\n                      <svg className=\"w-5 h-5 hover:text-yellow-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                        <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\" />\n                      </svg>\n                    )}\n                  </button>\n                </td>\n              </tr>\n              ))\n            )}\n          </tbody>\n        </table>\n      </div>\n    </div>\n  );\n};\n\nexport default FeaturedCourses;\n"], "mappings": ";;AAAA,OAAOA,KAAK,MAAgC,OAAO;AACnD,SAASC,QAAQ,QAAQ,uBAAuB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAcjD,MAAMC,eAAyB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EACtC,MAAM,CAACC,YAAY,EAAEC,eAAe,CAAC,GAAGP,KAAK,CAACQ,QAAQ,CAAW,CAAC,CAAC,CAAC,CAAC;EACrE,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGV,KAAK,CAACQ,QAAQ,CAAC,EAAE,CAAC;EACtD,MAAM,CAACG,MAAM,EAAEC,SAAS,CAAC,GAAGZ,KAAK,CAACQ,QAAQ,CAA+B,MAAM,CAAC;EAChF,MAAM;IAAEK,WAAW;IAAEC;EAAS,CAAC,GAAGb,QAAQ,CAAC,CAAC;EAE5C,MAAMc,WAAW,GAAGA,CAACC,QAAgB,EAAEC,UAAkB,KAAK;IAC5D,MAAMC,gBAAgB,GAAGZ,YAAY,CAACa,QAAQ,CAACH,QAAQ,CAAC;IACxDT,eAAe,CAACa,IAAI,IAClBA,IAAI,CAACD,QAAQ,CAACH,QAAQ,CAAC,GACnBI,IAAI,CAACC,MAAM,CAACC,EAAE,IAAIA,EAAE,KAAKN,QAAQ,CAAC,GAClC,CAAC,GAAGI,IAAI,EAAEJ,QAAQ,CACxB,CAAC;;IAED;IACA,IAAIE,gBAAgB,EAAE;MACpBJ,QAAQ,CAAC,YAAYG,UAAU,sBAAsB,CAAC;IACxD,CAAC,MAAM;MACLJ,WAAW,CAAC,UAAUI,UAAU,oBAAoB,CAAC;IACvD;EACF,CAAC;EAED,MAAMM,OAAiB,GAAG,CACxB;IACED,EAAE,EAAE,CAAC;IACLE,IAAI,EAAE,qBAAqB;IAC3BC,UAAU,EAAE,aAAa;IACzBC,gBAAgB,EAAE,2FAA2F;IAC7GC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE;EACT,CAAC,EACD;IACET,EAAE,EAAE,CAAC;IACLE,IAAI,EAAE,uBAAuB;IAC7BC,UAAU,EAAE,aAAa;IACzBC,gBAAgB,EAAE,2FAA2F;IAC7GC,SAAS,EAAE,QAAQ;IACnBC,QAAQ,EAAE,OAAO;IACjBC,IAAI,EAAE,WAAW;IACjBC,QAAQ,EAAE,QAAQ;IAClBC,KAAK,EAAE;EACT,CAAC,CACF;;EAED;EACA,MAAMC,eAAe,GAAGT,OAAO,CAC5BF,MAAM,CAACY,MAAM,IACZA,MAAM,CAACT,IAAI,CAACU,WAAW,CAAC,CAAC,CAACf,QAAQ,CAACV,UAAU,CAACyB,WAAW,CAAC,CAAC,CAAC,IAC5DD,MAAM,CAACR,UAAU,CAACS,WAAW,CAAC,CAAC,CAACf,QAAQ,CAACV,UAAU,CAACyB,WAAW,CAAC,CAAC,CACnE,CAAC,CACAC,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;IACd,QAAQ1B,MAAM;MACZ,KAAK,MAAM;QACT,OAAOyB,CAAC,CAACZ,IAAI,CAACc,aAAa,CAACD,CAAC,CAACb,IAAI,CAAC;MACrC,KAAK,MAAM;QACT,OAAO,IAAIe,IAAI,CAACH,CAAC,CAACT,SAAS,CAAC,CAACa,OAAO,CAAC,CAAC,GAAG,IAAID,IAAI,CAACF,CAAC,CAACV,SAAS,CAAC,CAACa,OAAO,CAAC,CAAC;MAC1E,KAAK,UAAU;QACb,OAAOC,QAAQ,CAACL,CAAC,CAACR,QAAQ,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,GAAGD,QAAQ,CAACJ,CAAC,CAACT,QAAQ,CAACc,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAChF;QACE,OAAO,CAAC;IACZ;EACF,CAAC,CAAC;EAEJ,oBACEvC,OAAA;IAAKwC,SAAS,EAAC,4CAA4C;IAAAC,QAAA,gBAEzDzC,OAAA;MAAKwC,SAAS,EAAC,8BAA8B;MAAAC,QAAA,gBAC3CzC,OAAA;QAAKwC,SAAS,EAAC,wCAAwC;QAAAC,QAAA,gBACrDzC,OAAA;UAAIwC,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAgB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzE7C,OAAA;UAAQwC,SAAS,EAAC,uDAAuD;UAAAC,QAAA,EAAC;QAE1E;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN7C,OAAA;QAAKwC,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CzC,OAAA;UAAKwC,SAAS,EAAC,iBAAiB;UAAAC,QAAA,eAC9BzC,OAAA;YAAKwC,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBzC,OAAA;cAAKwC,SAAS,EAAC,sEAAsE;cAAAC,QAAA,eACnFzC,OAAA;gBAAKwC,SAAS,EAAC,uBAAuB;gBAACM,IAAI,EAAC,MAAM;gBAACC,MAAM,EAAC,cAAc;gBAACC,OAAO,EAAC,WAAW;gBAAAP,QAAA,eAC1FzC,OAAA;kBAAMiD,aAAa,EAAC,OAAO;kBAACC,cAAc,EAAC,OAAO;kBAACC,WAAW,EAAE,CAAE;kBAACC,CAAC,EAAC;gBAA6C;kBAAAV,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN7C,OAAA;cACE0B,IAAI,EAAC,MAAM;cACX2B,WAAW,EAAC,mBAAmB;cAC/BC,KAAK,EAAEhD,UAAW;cAClBiD,QAAQ,EAAGC,CAAC,IAAKjD,aAAa,CAACiD,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;cAC/Cd,SAAS,EAAC;YAAqN;cAAAE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEN7C,OAAA;UACEsD,KAAK,EAAE9C,MAAO;UACd+C,QAAQ,EAAGC,CAAC,IAAK/C,SAAS,CAAC+C,CAAC,CAACC,MAAM,CAACH,KAAqC,CAAE;UAC3Ed,SAAS,EAAC,+HAA+H;UAAAC,QAAA,gBAEzIzC,OAAA;YAAQsD,KAAK,EAAC,MAAM;YAAAb,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1C7C,OAAA;YAAQsD,KAAK,EAAC,MAAM;YAAAb,QAAA,EAAC;UAAY;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAC1C7C,OAAA;YAAQsD,KAAK,EAAC,UAAU;YAAAb,QAAA,EAAC;UAAgB;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC5C,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGN7C,OAAA;MAAKwC,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC9BzC,OAAA;QAAOwC,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACvBzC,OAAA;UAAOwC,SAAS,EAAC,YAAY;UAAAC,QAAA,eAC3BzC,OAAA;YAAAyC,QAAA,gBACEzC,OAAA;cAAIwC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7C,OAAA;cAAIwC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7C,OAAA;cAAIwC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7C,OAAA;cAAIwC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC,eACL7C,OAAA;cAAIwC,SAAS,EAAC,gFAAgF;cAAAC,QAAA,EAAC;YAE/F;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAI,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC,eACR7C,OAAA;UAAOwC,SAAS,EAAC,mCAAmC;UAAAC,QAAA,EACjDZ,eAAe,CAAC6B,MAAM,KAAK,CAAC,gBAC3B1D,OAAA;YAAAyC,QAAA,eACEzC,OAAA;cAAI2D,OAAO,EAAE,CAAE;cAACnB,SAAS,EAAC,qCAAqC;cAAAC,QAAA,eAC7DzC,OAAA;gBAAKwC,SAAS,EAAC,4BAA4B;gBAAAC,QAAA,gBACzCzC,OAAA;kBAAKwC,SAAS,EAAC,8BAA8B;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACjGzC,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAqI;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1M,CAAC,eACN7C,OAAA;kBAAGwC,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAsC;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAG,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAC9D;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GAELhB,eAAe,CAAC+B,GAAG,CAAE9B,MAAM,iBAC3B9B,OAAA;YAAoBwC,SAAS,EAAC,kBAAkB;YAAAC,QAAA,gBAC9CzC,OAAA;cAAIwC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eACzCzC,OAAA;gBAAKwC,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,gBAChCzC,OAAA;kBAAKwC,SAAS,EAAC,yBAAyB;kBAAAC,QAAA,eACtCzC,OAAA;oBAAKwC,SAAS,EAAC,mEAAmE;oBAAAC,QAAA,eAChFzC,OAAA;sBAAMwC,SAAS,EAAC,qCAAqC;sBAAAC,QAAA,EAAC;oBAAE;sBAAAC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAM;kBAAC;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAC5D;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC,eACN7C,OAAA;kBAAKwC,SAAS,EAAC,MAAM;kBAAAC,QAAA,gBACnBzC,OAAA;oBAAKwC,SAAS,EAAC,mCAAmC;oBAAAC,QAAA,EAAEX,MAAM,CAACT;kBAAI;oBAAAqB,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eACtE7C,OAAA;oBAAKwC,SAAS,EAAC,wBAAwB;oBAAAC,QAAA,gBACrCzC,OAAA;sBACEwC,SAAS,EAAC,sBAAsB;sBAChCqB,GAAG,EAAE/B,MAAM,CAACP,gBAAiB;sBAC7BuC,GAAG,EAAEhC,MAAM,CAACR;oBAAW;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC,eACF7C,OAAA;sBAAMwC,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAEX,MAAM,CAACR;oBAAU;sBAAAoB,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAO,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACpE,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACJ,CAAC,eACL7C,OAAA;cAAIwC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DX,MAAM,CAACN;YAAS;cAAAkB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACL7C,OAAA;cAAIwC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,EAC9DX,MAAM,CAACL;YAAQ;cAAAiB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACd,CAAC,eACL7C,OAAA;cAAIwC,SAAS,EAAC,6BAA6B;cAAAC,QAAA,eACzCzC,OAAA;gBAAMwC,SAAS,EAAC,oFAAoF;gBAAAC,QAAA,EACjGX,MAAM,CAACJ;cAAI;gBAAAgB,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACR;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACL,CAAC,eACL7C,OAAA;cAAIwC,SAAS,EAAC,mDAAmD;cAAAC,QAAA,eAC/DzC,OAAA;gBACE+D,OAAO,EAAEA,CAAA,KAAMnD,WAAW,CAACkB,MAAM,CAACX,EAAE,EAAEW,MAAM,CAACT,IAAI,CAAE;gBACnDmB,SAAS,EAAC,kEAAkE;gBAAAC,QAAA,EAE3EtC,YAAY,CAACa,QAAQ,CAACc,MAAM,CAACX,EAAE,CAAC,gBAC/BnB,OAAA;kBAAKwC,SAAS,EAAC,+CAA+C;kBAACM,IAAI,EAAC,cAAc;kBAACE,OAAO,EAAC,WAAW;kBAAAP,QAAA,eACpGzC,OAAA;oBAAMoD,CAAC,EAAC;kBAA0V;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAClW,CAAC,gBAEN7C,OAAA;kBAAKwC,SAAS,EAAC,+BAA+B;kBAACM,IAAI,EAAC,MAAM;kBAACC,MAAM,EAAC,cAAc;kBAACC,OAAO,EAAC,WAAW;kBAAAP,QAAA,eAClGzC,OAAA;oBAAMiD,aAAa,EAAC,OAAO;oBAACC,cAAc,EAAC,OAAO;oBAACC,WAAW,EAAE,CAAE;oBAACC,CAAC,EAAC;kBAAmD;oBAAAV,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAE;gBAAC;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACxH;cACN;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACK;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACP,CAAC;UAAA,GA/CEf,MAAM,CAACX,EAAE;YAAAuB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgDd,CACH;QACF;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACI,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC3C,EAAA,CAxMID,eAAyB;EAAA,QAIKH,QAAQ;AAAA;AAAAkE,EAAA,GAJtC/D,eAAyB;AA0M/B,eAAeA,eAAe;AAAC,IAAA+D,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}