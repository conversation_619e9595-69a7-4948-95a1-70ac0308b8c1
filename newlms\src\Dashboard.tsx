import React, { useState } from 'react';
import Sidebar from './components/Sidebar.tsx';
import Header from './components/Header.tsx';
import WelcomeSection from './components/WelcomeSection.tsx';
import AnalyticsCards from './components/AnalyticsCards.tsx';
import FeaturedCourses from './components/FeaturedCourses.tsx';
import RightSidebar from './components/RightSidebar.tsx';
import ErrorBoundary from './components/ErrorBoundary.tsx';
import Toast from './components/Toast.tsx';
import { useToast } from './hooks/useToast.tsx';

const Dashboard: React.FC = () => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const { toast, hideToast, showSuccess } = useToast();

  // Example: Show welcome toast on mount
  React.useEffect(() => {
    const timer = setTimeout(() => {
      showSuccess('Welcome back to your dashboard!');
    }, 2000);
    return () => clearTimeout(timer);
  }, [showSuccess]);

  return (
    <ErrorBoundary>
      <div className="min-h-screen bg-gray-50 flex relative">
        {/* Mobile Sidebar Overlay */}
        {isMobileMenuOpen && (
          <div
            className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
            onClick={() => setIsMobileMenuOpen(false)}
          />
        )}

        {/* Left Sidebar */}
        <Sidebar
          isMobileMenuOpen={isMobileMenuOpen}
          setIsMobileMenuOpen={setIsMobileMenuOpen}
        />

        {/* Main Content */}
        <div className="flex-1 flex flex-col">
          {/* Header */}
          <Header
            onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          />

        {/* Main Dashboard Content */}
        <div className="flex-1 flex flex-col lg:flex-row">
          {/* Center Content */}
          <div className="flex-1 p-4 lg:p-6 space-y-6">
            {/* Welcome Section */}
            <WelcomeSection />

            {/* Analytics Cards */}
            <AnalyticsCards />

            {/* Featured Courses */}
            <FeaturedCourses />
          </div>

          {/* Right Sidebar */}
          <div className="lg:block">
            <RightSidebar />
          </div>
        </div>

        {/* Toast Notifications */}
        <Toast
          message={toast.message}
          type={toast.type}
          isVisible={toast.isVisible}
          onClose={hideToast}
        />
      </div>
    </ErrorBoundary>
  );
};

export default Dashboard;
