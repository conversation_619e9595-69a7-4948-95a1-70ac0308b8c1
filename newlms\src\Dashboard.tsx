import React from 'react';
import Sidebar from './components/Sidebar.tsx';
import Header from './components/Header.tsx';
import WelcomeSection from './components/WelcomeSection.tsx';
import AnalyticsCards from './components/AnalyticsCards.tsx';
import FeaturedCourses from './components/FeaturedCourses.tsx';
import RightSidebar from './components/RightSidebar.tsx';

const Dashboard: React.FC = () => {
  return (
    <div className="min-h-screen bg-gray-50 flex">
      {/* Left Sidebar */}
      <Sidebar />

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Header */}
        <Header />

        {/* Main Dashboard Content */}
        <div className="flex-1 flex flex-col lg:flex-row">
          {/* Center Content */}
          <div className="flex-1 p-4 lg:p-6 space-y-6">
            {/* Welcome Section */}
            <WelcomeSection />

            {/* Analytics Cards */}
            <AnalyticsCards />

            {/* Featured Courses */}
            <FeaturedCourses />
          </div>

          {/* Right Sidebar */}
          <div className="lg:block">
            <RightSidebar />
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
