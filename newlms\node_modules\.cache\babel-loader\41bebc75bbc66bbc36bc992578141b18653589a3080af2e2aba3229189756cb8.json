{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef, useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  onMobileMenuToggle\n}) => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [showMobileSearch, setShowMobileSearch] = useState(false);\n  const notificationRef = useRef(null);\n\n  // Close notifications when clicking outside\n  useEffect(() => {\n    const handleClickOutside = event => {\n      if (notificationRef.current && !notificationRef.current.contains(event.target)) {\n        setShowNotifications(false);\n      }\n    };\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white border-b border-gray-200 px-6 py-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: onMobileMenuToggle,\n          className: \"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M4 6h16M4 12h16M4 18h16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 37,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 36,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden lg:block\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Wednesday\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: \"September 6, 2023\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 45,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 43,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 50,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 49,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden md:flex flex-1 max-w-md mx-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-5 w-5 text-gray-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 57,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 56,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search for course\",\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 55,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 54,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowMobileSearch(!showMobileSearch),\n          className: \"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 78,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 77,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 73,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 72,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowNotifications(!showNotifications),\n            className: \"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 92,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 91,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 94,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this), showNotifications && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-semibold text-gray-900\",\n                children: \"Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 101,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 100,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-h-64 overflow-y-auto\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 hover:bg-gray-50 border-b border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-blue-500 rounded-full mt-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 106,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900\",\n                      children: \"New course available: Advanced React Patterns\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 108,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"2 minutes ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 109,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 107,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 105,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 104,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 hover:bg-gray-50 border-b border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-green-500 rounded-full mt-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 115,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900\",\n                      children: \"Assignment submitted successfully\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 117,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"1 hour ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 118,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 116,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 114,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 113,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 hover:bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-orange-500 rounded-full mt-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 124,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900\",\n                      children: \"Upcoming class: Programming Class at 10:00 AM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 126,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"3 hours ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 127,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 125,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 123,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 122,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 103,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"text-sm text-blue-600 hover:text-blue-700 font-medium\",\n                children: \"View all notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 133,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 132,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 99,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 86,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 rounded-full bg-gray-300 overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80\",\n              alt: \"Profile\",\n              className: \"w-full h-full object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 144,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 143,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-900\",\n              children: \"Corey George\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 151,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Student\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 152,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 150,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 142,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 84,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 29,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 28,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"xU3BxfLtyl2Hagyx+M+ZXa3x9h0=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "jsxDEV", "_jsxDEV", "Header", "onMobileMenuToggle", "_s", "searchQuery", "setSearch<PERSON>uery", "showNotifications", "setShowNotifications", "showMobileSearch", "setShowMobileSearch", "notificationRef", "handleClickOutside", "event", "current", "contains", "target", "document", "addEventListener", "removeEventListener", "className", "children", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "e", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/Header.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\ninterface HeaderProps {\n  onMobileMenuToggle: () => void;\n}\n\nconst Header: React.FC<HeaderProps> = ({ onMobileMenuToggle }) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [showMobileSearch, setShowMobileSearch] = useState(false);\n  const notificationRef = useRef<HTMLDivElement>(null);\n\n  // Close notifications when clicking outside\n  useEffect(() => {\n    const handleClickOutside = (event: MouseEvent) => {\n      if (notificationRef.current && !notificationRef.current.contains(event.target as Node)) {\n        setShowNotifications(false);\n      }\n    };\n\n    document.addEventListener('mousedown', handleClickOutside);\n    return () => {\n      document.removeEventListener('mousedown', handleClickOutside);\n    };\n  }, []);\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Mobile Menu Button */}\n        <div className=\"lg:hidden\">\n          <button\n            onClick={onMobileMenuToggle}\n            className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\"\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Date and Welcome */}\n        <div className=\"hidden lg:block\">\n          <h1 className=\"text-lg font-semibold text-gray-900\">Wednesday</h1>\n          <p className=\"text-sm text-gray-500\">September 6, 2023</p>\n        </div>\n\n        {/* Mobile Title */}\n        <div className=\"lg:hidden\">\n          <h1 className=\"text-lg font-semibold text-gray-900\">Dashboard</h1>\n        </div>\n\n        {/* Search Bar */}\n        <div className=\"hidden md:flex flex-1 max-w-md mx-8\">\n          <div className=\"relative w-full\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search for course\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n        </div>\n\n        {/* Mobile Search Button */}\n        <div className=\"md:hidden\">\n          <button\n            onClick={() => setShowMobileSearch(!showMobileSearch)}\n            className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\"\n          >\n            <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Right Side - Notifications and Profile */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Notifications */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowNotifications(!showNotifications)}\n              className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z\" />\n              </svg>\n              <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"></span>\n            </button>\n\n            {/* Notification Dropdown */}\n            {showNotifications && (\n              <div className=\"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">\n                <div className=\"p-4 border-b border-gray-200\">\n                  <h3 className=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                </div>\n                <div className=\"max-h-64 overflow-y-auto\">\n                  <div className=\"p-4 hover:bg-gray-50 border-b border-gray-100\">\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm text-gray-900\">New course available: Advanced React Patterns</p>\n                        <p className=\"text-xs text-gray-500 mt-1\">2 minutes ago</p>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"p-4 hover:bg-gray-50 border-b border-gray-100\">\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm text-gray-900\">Assignment submitted successfully</p>\n                        <p className=\"text-xs text-gray-500 mt-1\">1 hour ago</p>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"p-4 hover:bg-gray-50\">\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"w-2 h-2 bg-orange-500 rounded-full mt-2\"></div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm text-gray-900\">Upcoming class: Programming Class at 10:00 AM</p>\n                        <p className=\"text-xs text-gray-500 mt-1\">3 hours ago</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"p-3 border-t border-gray-200\">\n                  <button className=\"text-sm text-blue-600 hover:text-blue-700 font-medium\">\n                    View all notifications\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Profile */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 rounded-full bg-gray-300 overflow-hidden\">\n              <img\n                src=\"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80\"\n                alt=\"Profile\"\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n            <div className=\"hidden md:block\">\n              <p className=\"text-sm font-medium text-gray-900\">Corey George</p>\n              <p className=\"text-xs text-gray-500\">Student</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3D,MAAMC,MAA6B,GAAGA,CAAC;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGT,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACU,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGX,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACY,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGb,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMc,eAAe,GAAGb,MAAM,CAAiB,IAAI,CAAC;;EAEpD;EACAC,SAAS,CAAC,MAAM;IACd,MAAMa,kBAAkB,GAAIC,KAAiB,IAAK;MAChD,IAAIF,eAAe,CAACG,OAAO,IAAI,CAACH,eAAe,CAACG,OAAO,CAACC,QAAQ,CAACF,KAAK,CAACG,MAAc,CAAC,EAAE;QACtFR,oBAAoB,CAAC,KAAK,CAAC;MAC7B;IACF,CAAC;IAEDS,QAAQ,CAACC,gBAAgB,CAAC,WAAW,EAAEN,kBAAkB,CAAC;IAC1D,OAAO,MAAM;MACXK,QAAQ,CAACE,mBAAmB,CAAC,WAAW,EAAEP,kBAAkB,CAAC;IAC/D,CAAC;EACH,CAAC,EAAE,EAAE,CAAC;EAEN,oBACEX,OAAA;IAAQmB,SAAS,EAAC,6CAA6C;IAAAC,QAAA,eAC7DpB,OAAA;MAAKmB,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAEhDpB,OAAA;QAAKmB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBpB,OAAA;UACEqB,OAAO,EAAEnB,kBAAmB;UAC5BiB,SAAS,EAAC,0HAA0H;UAAAC,QAAA,eAEpIpB,OAAA;YAAKmB,SAAS,EAAC,SAAS;YAACG,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5EpB,OAAA;cAAMyB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNhC,OAAA;QAAKmB,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BpB,OAAA;UAAImB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClEhC,OAAA;UAAGmB,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAiB;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAGNhC,OAAA;QAAKmB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBpB,OAAA;UAAImB,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAS;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAGNhC,OAAA;QAAKmB,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClDpB,OAAA;UAAKmB,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BpB,OAAA;YAAKmB,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFpB,OAAA;cAAKmB,SAAS,EAAC,uBAAuB;cAACG,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC1FpB,OAAA;gBAAMyB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNhC,OAAA;YACEiC,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,mBAAmB;YAC/BC,KAAK,EAAE/B,WAAY;YACnBgC,QAAQ,EAAGC,CAAC,IAAKhC,cAAc,CAACgC,CAAC,CAACtB,MAAM,CAACoB,KAAK,CAAE;YAChDhB,SAAS,EAAC;UAA+M;YAAAU,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1N,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNhC,OAAA;QAAKmB,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBpB,OAAA;UACEqB,OAAO,EAAEA,CAAA,KAAMZ,mBAAmB,CAAC,CAACD,gBAAgB,CAAE;UACtDW,SAAS,EAAC,0HAA0H;UAAAC,QAAA,eAEpIpB,OAAA;YAAKmB,SAAS,EAAC,SAAS;YAACG,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAJ,QAAA,eAC5EpB,OAAA;cAAMyB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNhC,OAAA;QAAKmB,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1CpB,OAAA;UAAKmB,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBpB,OAAA;YACEqB,OAAO,EAAEA,CAAA,KAAMd,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;YACxDa,SAAS,EAAC,mIAAmI;YAAAC,QAAA,gBAE7IpB,OAAA;cAAKmB,SAAS,EAAC,SAAS;cAACG,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAJ,QAAA,eAC5EpB,OAAA;gBAAMyB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAmG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxK,CAAC,eACNhC,OAAA;cAAMmB,SAAS,EAAC;YAAgF;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,EAGR1B,iBAAiB,iBAChBN,OAAA;YAAKmB,SAAS,EAAC,sFAAsF;YAAAC,QAAA,gBACnGpB,OAAA;cAAKmB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CpB,OAAA;gBAAImB,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAa;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACNhC,OAAA;cAAKmB,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCpB,OAAA;gBAAKmB,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,eAC5DpB,OAAA;kBAAKmB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCpB,OAAA;oBAAKmB,SAAS,EAAC;kBAAuC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7DhC,OAAA;oBAAKmB,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBpB,OAAA;sBAAGmB,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6C;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACtFhC,OAAA;sBAAGmB,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhC,OAAA;gBAAKmB,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,eAC5DpB,OAAA;kBAAKmB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCpB,OAAA;oBAAKmB,SAAS,EAAC;kBAAwC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9DhC,OAAA;oBAAKmB,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBpB,OAAA;sBAAGmB,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAiC;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1EhC,OAAA;sBAAGmB,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAU;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACNhC,OAAA;gBAAKmB,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,eACnCpB,OAAA;kBAAKmB,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCpB,OAAA;oBAAKmB,SAAS,EAAC;kBAAyC;oBAAAU,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/DhC,OAAA;oBAAKmB,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBpB,OAAA;sBAAGmB,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6C;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACtFhC,OAAA;sBAAGmB,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAW;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACNhC,OAAA;cAAKmB,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CpB,OAAA;gBAAQmB,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAE1E;gBAAAS,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGNhC,OAAA;UAAKmB,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CpB,OAAA;YAAKmB,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eACjEpB,OAAA;cACEsC,GAAG,EAAC,2JAA2J;cAC/JC,GAAG,EAAC,SAAS;cACbpB,SAAS,EAAC;YAA4B;cAAAU,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACNhC,OAAA;YAAKmB,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BpB,OAAA;cAAGmB,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjEhC,OAAA;cAAGmB,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAO;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAAC7B,EAAA,CAxJIF,MAA6B;AAAAuC,EAAA,GAA7BvC,MAA6B;AA0JnC,eAAeA,MAAM;AAAC,IAAAuC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}