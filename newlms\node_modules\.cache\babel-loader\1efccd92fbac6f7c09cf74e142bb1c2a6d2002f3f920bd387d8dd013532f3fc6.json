{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\Header.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState, useRef } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Header = ({\n  onMobileMenuToggle\n}) => {\n  _s();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [showMobileSearch, setShowMobileSearch] = useState(false);\n  const notificationRef = useRef(null);\n  return /*#__PURE__*/_jsxDEV(\"header\", {\n    className: \"bg-white border-b border-gray-200 px-6 py-4\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setShowMobileMenu(!showMobileMenu),\n          className: \"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M4 6h16M4 12h16M4 18h16\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 25,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 24,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden lg:block\",\n        children: [/*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Wednesday\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 32,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-500\",\n          children: \"September 6, 2023\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 33,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 31,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"lg:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"h1\", {\n          className: \"text-lg font-semibold text-gray-900\",\n          children: \"Dashboard\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 38,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden md:flex flex-1 max-w-md mx-8\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative w-full\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-5 w-5 text-gray-400\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 46,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 45,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 44,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"input\", {\n            type: \"text\",\n            placeholder: \"Search for course\",\n            value: searchQuery,\n            onChange: e => setSearchQuery(e.target.value),\n            className: \"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 43,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"md:hidden\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-5 w-5\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 63,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 62,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 61,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 60,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-4\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative\",\n          children: [/*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setShowNotifications(!showNotifications),\n            className: \"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\",\n            children: [/*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"h-6 w-6\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 77,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 76,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 72,\n            columnNumber: 13\n          }, this), showNotifications && /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-4 border-b border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"text-sm font-semibold text-gray-900\",\n                children: \"Notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 86,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 85,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"max-h-64 overflow-y-auto\",\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 hover:bg-gray-50 border-b border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-blue-500 rounded-full mt-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 91,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900\",\n                      children: \"New course available: Advanced React Patterns\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 93,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"2 minutes ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 94,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 92,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 90,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 89,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 hover:bg-gray-50 border-b border-gray-100\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-green-500 rounded-full mt-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 100,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900\",\n                      children: \"Assignment submitted successfully\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 102,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"1 hour ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 103,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 101,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 99,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 98,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"p-4 hover:bg-gray-50\",\n                children: /*#__PURE__*/_jsxDEV(\"div\", {\n                  className: \"flex items-start space-x-3\",\n                  children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"w-2 h-2 bg-orange-500 rounded-full mt-2\"\n                  }, void 0, false, {\n                    fileName: _jsxFileName,\n                    lineNumber: 109,\n                    columnNumber: 23\n                  }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"flex-1\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-900\",\n                      children: \"Upcoming class: Programming Class at 10:00 AM\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 111,\n                      columnNumber: 25\n                    }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-xs text-gray-500 mt-1\",\n                      children: \"3 hours ago\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 112,\n                      columnNumber: 25\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 110,\n                    columnNumber: 23\n                  }, this)]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 108,\n                  columnNumber: 21\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 107,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 88,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"p-3 border-t border-gray-200\",\n              children: /*#__PURE__*/_jsxDEV(\"button\", {\n                className: \"text-sm text-blue-600 hover:text-blue-700 font-medium\",\n                children: \"View all notifications\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 118,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 117,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 84,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 71,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-10 h-10 rounded-full bg-gray-300 overflow-hidden\",\n            children: /*#__PURE__*/_jsxDEV(\"img\", {\n              src: \"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80\",\n              alt: \"Profile\",\n              className: \"w-full h-full object-cover\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 129,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 128,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"hidden md:block\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-sm font-medium text-gray-900\",\n              children: \"Corey George\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 136,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xs text-gray-500\",\n              children: \"Student\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 137,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 135,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 127,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 69,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 17,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(Header, \"zt+lUieTNk/aaBJRdhH/zuKttkA=\");\n_c = Header;\nexport default Header;\nvar _c;\n$RefreshReg$(_c, \"Header\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "jsxDEV", "_jsxDEV", "Header", "onMobileMenuToggle", "_s", "searchQuery", "setSearch<PERSON>uery", "showNotifications", "setShowNotifications", "showMobileSearch", "setShowMobileSearch", "notificationRef", "className", "children", "onClick", "setShowMobileMenu", "showMobileMenu", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "type", "placeholder", "value", "onChange", "e", "target", "src", "alt", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/Header.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\n\ninterface HeaderProps {\n  onMobileMenuToggle: () => void;\n}\n\nconst Header: React.FC<HeaderProps> = ({ onMobileMenuToggle }) => {\n  const [searchQuery, setSearchQuery] = useState('');\n  const [showNotifications, setShowNotifications] = useState(false);\n  const [showMobileSearch, setShowMobileSearch] = useState(false);\n  const notificationRef = useRef<HTMLDivElement>(null);\n\n\n\n  return (\n    <header className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Mobile Menu Button */}\n        <div className=\"lg:hidden\">\n          <button\n            onClick={() => setShowMobileMenu(!showMobileMenu)}\n            className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\"\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M4 6h16M4 12h16M4 18h16\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Date and Welcome */}\n        <div className=\"hidden lg:block\">\n          <h1 className=\"text-lg font-semibold text-gray-900\">Wednesday</h1>\n          <p className=\"text-sm text-gray-500\">September 6, 2023</p>\n        </div>\n\n        {/* Mobile Title */}\n        <div className=\"lg:hidden\">\n          <h1 className=\"text-lg font-semibold text-gray-900\">Dashboard</h1>\n        </div>\n\n        {/* Search Bar */}\n        <div className=\"hidden md:flex flex-1 max-w-md mx-8\">\n          <div className=\"relative w-full\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <svg className=\"h-5 w-5 text-gray-400\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n              </svg>\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Search for course\"\n              value={searchQuery}\n              onChange={(e) => setSearchQuery(e.target.value)}\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-gray-50 placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500\"\n            />\n          </div>\n        </div>\n\n        {/* Mobile Search Button */}\n        <div className=\"md:hidden\">\n          <button className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\">\n            <svg className=\"h-5 w-5\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z\" />\n            </svg>\n          </button>\n        </div>\n\n        {/* Right Side - Notifications and Profile */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Notifications */}\n          <div className=\"relative\">\n            <button\n              onClick={() => setShowNotifications(!showNotifications)}\n              className=\"relative p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\"\n            >\n              <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M15 17h5l-5 5v-5zM10.5 3.75a6 6 0 0 1 6 6v2.25l2.25 2.25v.75H2.25v-.75L4.5 12V9.75a6 6 0 0 1 6-6z\" />\n              </svg>\n              <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"></span>\n            </button>\n\n            {/* Notification Dropdown */}\n            {showNotifications && (\n              <div className=\"absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 z-50\">\n                <div className=\"p-4 border-b border-gray-200\">\n                  <h3 className=\"text-sm font-semibold text-gray-900\">Notifications</h3>\n                </div>\n                <div className=\"max-h-64 overflow-y-auto\">\n                  <div className=\"p-4 hover:bg-gray-50 border-b border-gray-100\">\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm text-gray-900\">New course available: Advanced React Patterns</p>\n                        <p className=\"text-xs text-gray-500 mt-1\">2 minutes ago</p>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"p-4 hover:bg-gray-50 border-b border-gray-100\">\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm text-gray-900\">Assignment submitted successfully</p>\n                        <p className=\"text-xs text-gray-500 mt-1\">1 hour ago</p>\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"p-4 hover:bg-gray-50\">\n                    <div className=\"flex items-start space-x-3\">\n                      <div className=\"w-2 h-2 bg-orange-500 rounded-full mt-2\"></div>\n                      <div className=\"flex-1\">\n                        <p className=\"text-sm text-gray-900\">Upcoming class: Programming Class at 10:00 AM</p>\n                        <p className=\"text-xs text-gray-500 mt-1\">3 hours ago</p>\n                      </div>\n                    </div>\n                  </div>\n                </div>\n                <div className=\"p-3 border-t border-gray-200\">\n                  <button className=\"text-sm text-blue-600 hover:text-blue-700 font-medium\">\n                    View all notifications\n                  </button>\n                </div>\n              </div>\n            )}\n          </div>\n\n          {/* Profile */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-10 h-10 rounded-full bg-gray-300 overflow-hidden\">\n              <img\n                src=\"https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?ixlib=rb-1.2.1&ixid=eyJhcHBfaWQiOjEyMDd9&auto=format&fit=facearea&facepad=2&w=256&h=256&q=80\"\n                alt=\"Profile\"\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n            <div className=\"hidden md:block\">\n              <p className=\"text-sm font-medium text-gray-900\">Corey George</p>\n              <p className=\"text-xs text-gray-500\">Student</p>\n            </div>\n          </div>\n        </div>\n      </div>\n    </header>\n  );\n};\n\nexport default Header;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,QAAmB,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAM3D,MAAMC,MAA6B,GAAGA,CAAC;EAAEC;AAAmB,CAAC,KAAK;EAAAC,EAAA;EAChE,MAAM,CAACC,WAAW,EAAEC,cAAc,CAAC,GAAGR,QAAQ,CAAC,EAAE,CAAC;EAClD,MAAM,CAACS,iBAAiB,EAAEC,oBAAoB,CAAC,GAAGV,QAAQ,CAAC,KAAK,CAAC;EACjE,MAAM,CAACW,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGZ,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAMa,eAAe,GAAGZ,MAAM,CAAiB,IAAI,CAAC;EAIpD,oBACEE,OAAA;IAAQW,SAAS,EAAC,6CAA6C;IAAAC,QAAA,eAC7DZ,OAAA;MAAKW,SAAS,EAAC,mCAAmC;MAAAC,QAAA,gBAEhDZ,OAAA;QAAKW,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBZ,OAAA;UACEa,OAAO,EAAEA,CAAA,KAAMC,iBAAiB,CAAC,CAACC,cAAc,CAAE;UAClDJ,SAAS,EAAC,0HAA0H;UAAAC,QAAA,eAEpIZ,OAAA;YAAKW,SAAS,EAAC,SAAS;YAACK,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAN,QAAA,eAC5EZ,OAAA;cAAMmB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAyB;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC9F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN1B,OAAA;QAAKW,SAAS,EAAC,iBAAiB;QAAAC,QAAA,gBAC9BZ,OAAA;UAAIW,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAS;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eAClE1B,OAAA;UAAGW,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAC;QAAiB;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACvD,CAAC,eAGN1B,OAAA;QAAKW,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBZ,OAAA;UAAIW,SAAS,EAAC,qCAAqC;UAAAC,QAAA,EAAC;QAAS;UAAAW,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D,CAAC,eAGN1B,OAAA;QAAKW,SAAS,EAAC,qCAAqC;QAAAC,QAAA,eAClDZ,OAAA;UAAKW,SAAS,EAAC,iBAAiB;UAAAC,QAAA,gBAC9BZ,OAAA;YAAKW,SAAS,EAAC,sEAAsE;YAAAC,QAAA,eACnFZ,OAAA;cAAKW,SAAS,EAAC,uBAAuB;cAACK,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAN,QAAA,eAC1FZ,OAAA;gBAAMmB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA6C;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAClH;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1B,OAAA;YACE2B,IAAI,EAAC,MAAM;YACXC,WAAW,EAAC,mBAAmB;YAC/BC,KAAK,EAAEzB,WAAY;YACnB0B,QAAQ,EAAGC,CAAC,IAAK1B,cAAc,CAAC0B,CAAC,CAACC,MAAM,CAACH,KAAK,CAAE;YAChDlB,SAAS,EAAC;UAA+M;YAAAY,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1N,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGN1B,OAAA;QAAKW,SAAS,EAAC,WAAW;QAAAC,QAAA,eACxBZ,OAAA;UAAQW,SAAS,EAAC,0HAA0H;UAAAC,QAAA,eAC1IZ,OAAA;YAAKW,SAAS,EAAC,SAAS;YAACK,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAN,QAAA,eAC5EZ,OAAA;cAAMmB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAA6C;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClH;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGN1B,OAAA;QAAKW,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAE1CZ,OAAA;UAAKW,SAAS,EAAC,UAAU;UAAAC,QAAA,gBACvBZ,OAAA;YACEa,OAAO,EAAEA,CAAA,KAAMN,oBAAoB,CAAC,CAACD,iBAAiB,CAAE;YACxDK,SAAS,EAAC,mIAAmI;YAAAC,QAAA,gBAE7IZ,OAAA;cAAKW,SAAS,EAAC,SAAS;cAACK,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAN,QAAA,eAC5EZ,OAAA;gBAAMmB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAAmG;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxK,CAAC,eACN1B,OAAA;cAAMW,SAAS,EAAC;YAAgF;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClG,CAAC,EAGRpB,iBAAiB,iBAChBN,OAAA;YAAKW,SAAS,EAAC,sFAAsF;YAAAC,QAAA,gBACnGZ,OAAA;cAAKW,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CZ,OAAA;gBAAIW,SAAS,EAAC,qCAAqC;gBAAAC,QAAA,EAAC;cAAa;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACnE,CAAC,eACN1B,OAAA;cAAKW,SAAS,EAAC,0BAA0B;cAAAC,QAAA,gBACvCZ,OAAA;gBAAKW,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,eAC5DZ,OAAA;kBAAKW,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCZ,OAAA;oBAAKW,SAAS,EAAC;kBAAuC;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC7D1B,OAAA;oBAAKW,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBZ,OAAA;sBAAGW,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6C;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACtF1B,OAAA;sBAAGW,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAa;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACxD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1B,OAAA;gBAAKW,SAAS,EAAC,+CAA+C;gBAAAC,QAAA,eAC5DZ,OAAA;kBAAKW,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCZ,OAAA;oBAAKW,SAAS,EAAC;kBAAwC;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC9D1B,OAAA;oBAAKW,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBZ,OAAA;sBAAGW,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAAiC;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC1E1B,OAAA;sBAAGW,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAU;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACrD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC,eACN1B,OAAA;gBAAKW,SAAS,EAAC,sBAAsB;gBAAAC,QAAA,eACnCZ,OAAA;kBAAKW,SAAS,EAAC,4BAA4B;kBAAAC,QAAA,gBACzCZ,OAAA;oBAAKW,SAAS,EAAC;kBAAyC;oBAAAY,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OAAM,CAAC,eAC/D1B,OAAA;oBAAKW,SAAS,EAAC,QAAQ;oBAAAC,QAAA,gBACrBZ,OAAA;sBAAGW,SAAS,EAAC,uBAAuB;sBAAAC,QAAA,EAAC;oBAA6C;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eACtF1B,OAAA;sBAAGW,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAW;sBAAAW,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACtD,CAAC;gBAAA;kBAAAH,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACH;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eACN1B,OAAA;cAAKW,SAAS,EAAC,8BAA8B;cAAAC,QAAA,eAC3CZ,OAAA;gBAAQW,SAAS,EAAC,uDAAuD;gBAAAC,QAAA,EAAC;cAE1E;gBAAAW,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAQ;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CACN;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAGN1B,OAAA;UAAKW,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1CZ,OAAA;YAAKW,SAAS,EAAC,oDAAoD;YAAAC,QAAA,eACjEZ,OAAA;cACEiC,GAAG,EAAC,2JAA2J;cAC/JC,GAAG,EAAC,SAAS;cACbvB,SAAS,EAAC;YAA4B;cAAAY,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACvC;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eACN1B,OAAA;YAAKW,SAAS,EAAC,iBAAiB;YAAAC,QAAA,gBAC9BZ,OAAA;cAAGW,SAAS,EAAC,mCAAmC;cAAAC,QAAA,EAAC;YAAY;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjE1B,OAAA;cAAGW,SAAS,EAAC,uBAAuB;cAAAC,QAAA,EAAC;YAAO;cAAAW,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC7C,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACA,CAAC;AAEb,CAAC;AAACvB,EAAA,CAzIIF,MAA6B;AAAAkC,EAAA,GAA7BlC,MAA6B;AA2InC,eAAeA,MAAM;AAAC,IAAAkC,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}