import React, { useState, useEffect } from 'react';

const PerformanceChart: React.FC = () => {
  const [animatedProgress, setAnimatedProgress] = useState(0);
  const performanceData = {
    points: 8966,
    progress: 75, // percentage
    pointsProgress: 'Point Progress',
    pointsDecrease: 'Point Decrease'
  };

  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimatedProgress(performanceData.progress);
    }, 500);
    return () => clearTimeout(timer);
  }, [performanceData.progress]);

  const radius = 60;
  const strokeWidth = 8;
  const normalizedRadius = radius - strokeWidth * 2;
  const circumference = normalizedRadius * 2 * Math.PI;
  const strokeDasharray = `${circumference} ${circumference}`;
  const strokeDashoffset = circumference - (animatedProgress / 100) * circumference;

  return (
    <div className="text-center">
      {/* Legend */}
      <div className="flex items-center justify-center space-x-4 mb-6">
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-orange-400 rounded-full"></div>
          <span className="text-xs text-gray-600">{performanceData.pointsProgress}</span>
        </div>
        <div className="flex items-center space-x-2">
          <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
          <span className="text-xs text-gray-600">{performanceData.pointsDecrease}</span>
        </div>
      </div>

      {/* Circular Progress Chart */}
      <div className="relative inline-flex items-center justify-center">
        <svg
          height={radius * 2}
          width={radius * 2}
          className="transform -rotate-90"
        >
          {/* Background circle */}
          <circle
            stroke="#e5e7eb"
            fill="transparent"
            strokeWidth={strokeWidth}
            r={normalizedRadius}
            cx={radius}
            cy={radius}
          />
          {/* Progress circle */}
          <circle
            stroke="#fb923c"
            fill="transparent"
            strokeWidth={strokeWidth}
            strokeDasharray={strokeDasharray}
            style={{ strokeDashoffset }}
            strokeLinecap="round"
            r={normalizedRadius}
            cx={radius}
            cy={radius}
            className="transition-all duration-1000 ease-out"
          />
        </svg>

        {/* Center content */}
        <div className="absolute inset-0 flex flex-col items-center justify-center">
          <div className="text-2xl font-bold text-gray-900">{performanceData.points.toLocaleString()}</div>
          <div className="text-xs text-gray-500">Your Point</div>
        </div>
      </div>

      {/* Performance indicators */}
      <div className="mt-6 flex items-center justify-center space-x-8">
        {/* Left avatar */}
        <div className="flex flex-col items-center">
          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-blue-600 text-lg">👨‍💼</span>
          </div>
        </div>

        {/* Center indicator */}
        <div className="flex flex-col items-center">
          <div className="w-12 h-12 bg-orange-100 rounded-full flex items-center justify-center mb-2 relative">
            <span className="text-orange-600 text-xl">⭐</span>
            <div className="absolute -top-1 -right-1 w-4 h-4 bg-orange-400 rounded-full"></div>
          </div>
        </div>

        {/* Right avatar */}
        <div className="flex flex-col items-center">
          <div className="w-10 h-10 bg-gray-100 rounded-full flex items-center justify-center mb-2">
            <span className="text-gray-600 text-lg">👩‍💼</span>
          </div>
        </div>
      </div>
    </div>
  );
};

export default PerformanceChart;
