{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\Toast.tsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Toast = ({\n  message,\n  type,\n  isVisible,\n  onClose,\n  duration = 3000\n}) => {\n  _s();\n  useEffect(() => {\n    if (isVisible && duration > 0) {\n      const timer = setTimeout(() => {\n        onClose();\n      }, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [isVisible, duration, onClose]);\n  if (!isVisible) return null;\n  const getToastStyles = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800';\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800';\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800';\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800';\n    }\n  };\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5 text-green-400\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 49,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 48,\n          columnNumber: 11\n        }, this);\n      case 'error':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5 text-red-400\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 55,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 54,\n          columnNumber: 11\n        }, this);\n      case 'warning':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5 text-yellow-400\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 60,\n          columnNumber: 11\n        }, this);\n      case 'info':\n        return /*#__PURE__*/_jsxDEV(\"svg\", {\n          className: \"w-5 h-5 text-blue-400\",\n          fill: \"currentColor\",\n          viewBox: \"0 0 20 20\",\n          children: /*#__PURE__*/_jsxDEV(\"path\", {\n            fillRule: \"evenodd\",\n            d: \"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\",\n            clipRule: \"evenodd\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 67,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this);\n      default:\n        return null;\n    }\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"fixed top-4 right-4 z-50 animate-slide-up\",\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `max-w-sm w-full border rounded-lg p-4 shadow-lg ${getToastStyles()}`,\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-shrink-0\",\n          children: getIcon()\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 79,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-3 flex-1\",\n          children: /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-sm font-medium\",\n            children: message\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 83,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 82,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"ml-4 flex-shrink-0\",\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: onClose,\n            className: \"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-4 h-4\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M6 18L18 6M6 6l12 12\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 91,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 90,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 86,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 85,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 78,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 77,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 76,\n    columnNumber: 5\n  }, this);\n};\n_s(Toast, \"OD7bBpZva5O2jO+Puf00hKivP7c=\");\n_c = Toast;\nexport default Toast;\nvar _c;\n$RefreshReg$(_c, \"Toast\");", "map": {"version": 3, "names": ["React", "useEffect", "jsxDEV", "_jsxDEV", "Toast", "message", "type", "isVisible", "onClose", "duration", "_s", "timer", "setTimeout", "clearTimeout", "getToastStyles", "getIcon", "className", "fill", "viewBox", "children", "fillRule", "d", "clipRule", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "stroke", "strokeLinecap", "strokeLinejoin", "strokeWidth", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/Toast.tsx"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\n\ninterface ToastProps {\n  message: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  isVisible: boolean;\n  onClose: () => void;\n  duration?: number;\n}\n\nconst Toast: React.FC<ToastProps> = ({ \n  message, \n  type, \n  isVisible, \n  onClose, \n  duration = 3000 \n}) => {\n  useEffect(() => {\n    if (isVisible && duration > 0) {\n      const timer = setTimeout(() => {\n        onClose();\n      }, duration);\n      return () => clearTimeout(timer);\n    }\n  }, [isVisible, duration, onClose]);\n\n  if (!isVisible) return null;\n\n  const getToastStyles = () => {\n    switch (type) {\n      case 'success':\n        return 'bg-green-50 border-green-200 text-green-800';\n      case 'error':\n        return 'bg-red-50 border-red-200 text-red-800';\n      case 'warning':\n        return 'bg-yellow-50 border-yellow-200 text-yellow-800';\n      case 'info':\n        return 'bg-blue-50 border-blue-200 text-blue-800';\n      default:\n        return 'bg-gray-50 border-gray-200 text-gray-800';\n    }\n  };\n\n  const getIcon = () => {\n    switch (type) {\n      case 'success':\n        return (\n          <svg className=\"w-5 h-5 text-green-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'error':\n        return (\n          <svg className=\"w-5 h-5 text-red-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'warning':\n        return (\n          <svg className=\"w-5 h-5 text-yellow-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      case 'info':\n        return (\n          <svg className=\"w-5 h-5 text-blue-400\" fill=\"currentColor\" viewBox=\"0 0 20 20\">\n            <path fillRule=\"evenodd\" d=\"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z\" clipRule=\"evenodd\" />\n          </svg>\n        );\n      default:\n        return null;\n    }\n  };\n\n  return (\n    <div className=\"fixed top-4 right-4 z-50 animate-slide-up\">\n      <div className={`max-w-sm w-full border rounded-lg p-4 shadow-lg ${getToastStyles()}`}>\n        <div className=\"flex items-center\">\n          <div className=\"flex-shrink-0\">\n            {getIcon()}\n          </div>\n          <div className=\"ml-3 flex-1\">\n            <p className=\"text-sm font-medium\">{message}</p>\n          </div>\n          <div className=\"ml-4 flex-shrink-0\">\n            <button\n              onClick={onClose}\n              className=\"inline-flex text-gray-400 hover:text-gray-600 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500\"\n            >\n              <svg className=\"w-4 h-4\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n              </svg>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Toast;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAcC,SAAS,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAUnD,MAAMC,KAA2B,GAAGA,CAAC;EACnCC,OAAO;EACPC,IAAI;EACJC,SAAS;EACTC,OAAO;EACPC,QAAQ,GAAG;AACb,CAAC,KAAK;EAAAC,EAAA;EACJT,SAAS,CAAC,MAAM;IACd,IAAIM,SAAS,IAAIE,QAAQ,GAAG,CAAC,EAAE;MAC7B,MAAME,KAAK,GAAGC,UAAU,CAAC,MAAM;QAC7BJ,OAAO,CAAC,CAAC;MACX,CAAC,EAAEC,QAAQ,CAAC;MACZ,OAAO,MAAMI,YAAY,CAACF,KAAK,CAAC;IAClC;EACF,CAAC,EAAE,CAACJ,SAAS,EAAEE,QAAQ,EAAED,OAAO,CAAC,CAAC;EAElC,IAAI,CAACD,SAAS,EAAE,OAAO,IAAI;EAE3B,MAAMO,cAAc,GAAGA,CAAA,KAAM;IAC3B,QAAQR,IAAI;MACV,KAAK,SAAS;QACZ,OAAO,6CAA6C;MACtD,KAAK,OAAO;QACV,OAAO,uCAAuC;MAChD,KAAK,SAAS;QACZ,OAAO,gDAAgD;MACzD,KAAK,MAAM;QACT,OAAO,0CAA0C;MACnD;QACE,OAAO,0CAA0C;IACrD;EACF,CAAC;EAED,MAAMS,OAAO,GAAGA,CAAA,KAAM;IACpB,QAAQT,IAAI;MACV,KAAK,SAAS;QACZ,oBACEH,OAAA;UAAKa,SAAS,EAAC,wBAAwB;UAACC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC7EhB,OAAA;YAAMiB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,uIAAuI;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrL,CAAC;MAEV,KAAK,OAAO;QACV,oBACEvB,OAAA;UAAKa,SAAS,EAAC,sBAAsB;UAACC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC3EhB,OAAA;YAAMiB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,yNAAyN;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACvQ,CAAC;MAEV,KAAK,SAAS;QACZ,oBACEvB,OAAA;UAAKa,SAAS,EAAC,yBAAyB;UAACC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC9EhB,OAAA;YAAMiB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,mNAAmN;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACjQ,CAAC;MAEV,KAAK,MAAM;QACT,oBACEvB,OAAA;UAAKa,SAAS,EAAC,uBAAuB;UAACC,IAAI,EAAC,cAAc;UAACC,OAAO,EAAC,WAAW;UAAAC,QAAA,eAC5EhB,OAAA;YAAMiB,QAAQ,EAAC,SAAS;YAACC,CAAC,EAAC,kIAAkI;YAACC,QAAQ,EAAC;UAAS;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChL,CAAC;MAEV;QACE,OAAO,IAAI;IACf;EACF,CAAC;EAED,oBACEvB,OAAA;IAAKa,SAAS,EAAC,2CAA2C;IAAAG,QAAA,eACxDhB,OAAA;MAAKa,SAAS,EAAE,mDAAmDF,cAAc,CAAC,CAAC,EAAG;MAAAK,QAAA,eACpFhB,OAAA;QAAKa,SAAS,EAAC,mBAAmB;QAAAG,QAAA,gBAChChB,OAAA;UAAKa,SAAS,EAAC,eAAe;UAAAG,QAAA,EAC3BJ,OAAO,CAAC;QAAC;UAAAQ,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,eACNvB,OAAA;UAAKa,SAAS,EAAC,aAAa;UAAAG,QAAA,eAC1BhB,OAAA;YAAGa,SAAS,EAAC,qBAAqB;YAAAG,QAAA,EAAEd;UAAO;YAAAkB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC7C,CAAC,eACNvB,OAAA;UAAKa,SAAS,EAAC,oBAAoB;UAAAG,QAAA,eACjChB,OAAA;YACEwB,OAAO,EAAEnB,OAAQ;YACjBQ,SAAS,EAAC,uHAAuH;YAAAG,QAAA,eAEjIhB,OAAA;cAAKa,SAAS,EAAC,SAAS;cAACC,IAAI,EAAC,MAAM;cAACW,MAAM,EAAC,cAAc;cAACV,OAAO,EAAC,WAAW;cAAAC,QAAA,eAC5EhB,OAAA;gBAAM0B,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACV,CAAC,EAAC;cAAsB;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3F;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACA;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAChB,EAAA,CAxFIN,KAA2B;AAAA4B,EAAA,GAA3B5B,KAA2B;AA0FjC,eAAeA,KAAK;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}