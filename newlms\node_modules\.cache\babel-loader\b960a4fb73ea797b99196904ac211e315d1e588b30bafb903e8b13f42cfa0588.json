{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\ErrorBoundary.tsx\";\nimport React, { Component } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nclass ErrorBoundary extends Component {\n  constructor(...args) {\n    super(...args);\n    this.state = {\n      hasError: false\n    };\n  }\n  static getDerivedStateFromError(error) {\n    return {\n      hasError: true,\n      error\n    };\n  }\n  componentDidCatch(error, errorInfo) {\n    console.error('Uncaught error:', error, errorInfo);\n  }\n  render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n      return /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"min-h-screen flex items-center justify-center bg-gray-50\",\n        children: /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"svg\", {\n              className: \"w-8 h-8 text-red-600\",\n              fill: \"none\",\n              stroke: \"currentColor\",\n              viewBox: \"0 0 24 24\",\n              children: /*#__PURE__*/_jsxDEV(\"path\", {\n                strokeLinecap: \"round\",\n                strokeLinejoin: \"round\",\n                strokeWidth: 2,\n                d: \"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n            className: \"text-xl font-semibold text-gray-900 mb-2\",\n            children: \"Something went wrong\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 40,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n            className: \"text-gray-600 mb-4\",\n            children: \"We're sorry, but something unexpected happened. Please try refreshing the page.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 41,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => window.location.reload(),\n            className: \"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\",\n            children: \"Refresh Page\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this);\n    }\n    return this.props.children;\n  }\n}\nexport default ErrorBoundary;", "map": {"version": 3, "names": ["React", "Component", "jsxDEV", "_jsxDEV", "Error<PERSON>ou<PERSON><PERSON>", "constructor", "args", "state", "<PERSON><PERSON><PERSON><PERSON>", "getDerivedStateFromError", "error", "componentDidCatch", "errorInfo", "console", "render", "props", "fallback", "className", "children", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "window", "location", "reload"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/ErrorBoundary.tsx"], "sourcesContent": ["import React, { Component, ErrorInfo, ReactNode } from 'react';\n\ninterface Props {\n  children: ReactNode;\n  fallback?: ReactNode;\n}\n\ninterface State {\n  hasError: boolean;\n  error?: Error;\n}\n\nclass ErrorBoundary extends Component<Props, State> {\n  public state: State = {\n    hasError: false\n  };\n\n  public static getDerivedStateFromError(error: Error): State {\n    return { hasError: true, error };\n  }\n\n  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {\n    console.error('Uncaught error:', error, errorInfo);\n  }\n\n  public render() {\n    if (this.state.hasError) {\n      if (this.props.fallback) {\n        return this.props.fallback;\n      }\n\n      return (\n        <div className=\"min-h-screen flex items-center justify-center bg-gray-50\">\n          <div className=\"max-w-md w-full bg-white rounded-lg shadow-lg p-6 text-center\">\n            <div className=\"w-16 h-16 mx-auto mb-4 bg-red-100 rounded-full flex items-center justify-center\">\n              <svg className=\"w-8 h-8 text-red-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z\" />\n              </svg>\n            </div>\n            <h2 className=\"text-xl font-semibold text-gray-900 mb-2\">Something went wrong</h2>\n            <p className=\"text-gray-600 mb-4\">We're sorry, but something unexpected happened. Please try refreshing the page.</p>\n            <button\n              onClick={() => window.location.reload()}\n              className=\"bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors\"\n            >\n              Refresh Page\n            </button>\n          </div>\n        </div>\n      );\n    }\n\n    return this.props.children;\n  }\n}\n\nexport default ErrorBoundary;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,SAAS,QAA8B,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAY/D,MAAMC,aAAa,SAASH,SAAS,CAAe;EAAAI,YAAA,GAAAC,IAAA;IAAA,SAAAA,IAAA;IAAA,KAC3CC,KAAK,GAAU;MACpBC,QAAQ,EAAE;IACZ,CAAC;EAAA;EAED,OAAcC,wBAAwBA,CAACC,KAAY,EAAS;IAC1D,OAAO;MAAEF,QAAQ,EAAE,IAAI;MAAEE;IAAM,CAAC;EAClC;EAEOC,iBAAiBA,CAACD,KAAY,EAAEE,SAAoB,EAAE;IAC3DC,OAAO,CAACH,KAAK,CAAC,iBAAiB,EAAEA,KAAK,EAAEE,SAAS,CAAC;EACpD;EAEOE,MAAMA,CAAA,EAAG;IACd,IAAI,IAAI,CAACP,KAAK,CAACC,QAAQ,EAAE;MACvB,IAAI,IAAI,CAACO,KAAK,CAACC,QAAQ,EAAE;QACvB,OAAO,IAAI,CAACD,KAAK,CAACC,QAAQ;MAC5B;MAEA,oBACEb,OAAA;QAAKc,SAAS,EAAC,0DAA0D;QAAAC,QAAA,eACvEf,OAAA;UAAKc,SAAS,EAAC,+DAA+D;UAAAC,QAAA,gBAC5Ef,OAAA;YAAKc,SAAS,EAAC,iFAAiF;YAAAC,QAAA,eAC9Ff,OAAA;cAAKc,SAAS,EAAC,sBAAsB;cAACE,IAAI,EAAC,MAAM;cAACC,MAAM,EAAC,cAAc;cAACC,OAAO,EAAC,WAAW;cAAAH,QAAA,eACzFf,OAAA;gBAAMmB,aAAa,EAAC,OAAO;gBAACC,cAAc,EAAC,OAAO;gBAACC,WAAW,EAAE,CAAE;gBAACC,CAAC,EAAC;cAA2I;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChN;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACN1B,OAAA;YAAIc,SAAS,EAAC,0CAA0C;YAAAC,QAAA,EAAC;UAAoB;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAI,CAAC,eAClF1B,OAAA;YAAGc,SAAS,EAAC,oBAAoB;YAAAC,QAAA,EAAC;UAA+E;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAG,CAAC,eACrH1B,OAAA;YACE2B,OAAO,EAAEA,CAAA,KAAMC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC,CAAE;YACxChB,SAAS,EAAC,iFAAiF;YAAAC,QAAA,EAC5F;UAED;YAAAQ,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC;IAEV;IAEA,OAAO,IAAI,CAACd,KAAK,CAACG,QAAQ;EAC5B;AACF;AAEA,eAAed,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}