{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\Dashboard.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport Sidebar from './components/Sidebar.tsx';\nimport Header from './components/Header.tsx';\nimport WelcomeSection from './components/WelcomeSection.tsx';\nimport AnalyticsCards from './components/AnalyticsCards.tsx';\nimport FeaturedCourses from './components/FeaturedCourses.tsx';\nimport RightSidebar from './components/RightSidebar.tsx';\nimport ErrorBoundary from './components/ErrorBoundary.tsx';\nimport Toast from './components/Toast.tsx';\nimport { useToast } from './hooks/useToast.tsx';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Dashboard = () => {\n  _s();\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const {\n    toast,\n    hideToast,\n    showSuccess\n  } = useToast();\n\n  // Example: Show welcome toast on mount\n  React.useEffect(() => {\n    const timer = setTimeout(() => {\n      showSuccess('Welcome back to your dashboard!');\n    }, 2000);\n    return () => clearTimeout(timer);\n  }, [showSuccess]);\n  return /*#__PURE__*/_jsxDEV(ErrorBoundary, {\n    children: /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"min-h-screen bg-gray-50 flex relative\",\n      children: [isMobileMenuOpen && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\",\n        onClick: () => setIsMobileMenuOpen(false)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Sidebar, {\n        isMobileMenuOpen: isMobileMenuOpen,\n        setIsMobileMenuOpen: setIsMobileMenuOpen\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 36,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 flex flex-col\",\n        children: [/*#__PURE__*/_jsxDEV(Header, {\n          onMobileMenuToggle: () => setIsMobileMenuOpen(!isMobileMenuOpen)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 44,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex-1 flex flex-col lg:flex-row\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 p-4 lg:p-6 space-y-6\",\n            children: [/*#__PURE__*/_jsxDEV(WelcomeSection, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(AnalyticsCards, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 13\n            }, this), /*#__PURE__*/_jsxDEV(FeaturedCourses, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 59,\n              columnNumber: 13\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 51,\n            columnNumber: 11\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"lg:block\",\n            children: /*#__PURE__*/_jsxDEV(RightSidebar, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 64,\n              columnNumber: 13\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 63,\n            columnNumber: 11\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 49,\n          columnNumber: 9\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 42,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Toast, {\n        message: toast.message,\n        type: toast.type,\n        isVisible: toast.isVisible,\n        onClose: hideToast\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 70,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 26,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 25,\n    columnNumber: 5\n  }, this);\n};\n_s(Dashboard, \"YIMBhMjYZDvBYflhESpiShyXHrU=\", false, function () {\n  return [useToast];\n});\n_c = Dashboard;\nexport default Dashboard;\nvar _c;\n$RefreshReg$(_c, \"Dashboard\");", "map": {"version": 3, "names": ["React", "useState", "Sidebar", "Header", "WelcomeSection", "AnalyticsCards", "FeaturedCourses", "RightSidebar", "Error<PERSON>ou<PERSON><PERSON>", "Toast", "useToast", "jsxDEV", "_jsxDEV", "Dashboard", "_s", "isMobileMenuOpen", "setIsMobileMenuOpen", "toast", "hideToast", "showSuccess", "useEffect", "timer", "setTimeout", "clearTimeout", "children", "className", "onClick", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onMobileMenuToggle", "message", "type", "isVisible", "onClose", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/Dashboard.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport Sidebar from './components/Sidebar.tsx';\nimport Header from './components/Header.tsx';\nimport WelcomeSection from './components/WelcomeSection.tsx';\nimport AnalyticsCards from './components/AnalyticsCards.tsx';\nimport FeaturedCourses from './components/FeaturedCourses.tsx';\nimport RightSidebar from './components/RightSidebar.tsx';\nimport ErrorBoundary from './components/ErrorBoundary.tsx';\nimport Toast from './components/Toast.tsx';\nimport { useToast } from './hooks/useToast.tsx';\n\nconst Dashboard: React.FC = () => {\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);\n  const { toast, hideToast, showSuccess } = useToast();\n\n  // Example: Show welcome toast on mount\n  React.useEffect(() => {\n    const timer = setTimeout(() => {\n      showSuccess('Welcome back to your dashboard!');\n    }, 2000);\n    return () => clearTimeout(timer);\n  }, [showSuccess]);\n\n  return (\n    <ErrorBoundary>\n      <div className=\"min-h-screen bg-gray-50 flex relative\">\n        {/* Mobile Sidebar Overlay */}\n        {isMobileMenuOpen && (\n          <div\n            className=\"fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden\"\n            onClick={() => setIsMobileMenuOpen(false)}\n          />\n        )}\n\n        {/* Left Sidebar */}\n        <Sidebar\n          isMobileMenuOpen={isMobileMenuOpen}\n          setIsMobileMenuOpen={setIsMobileMenuOpen}\n        />\n\n        {/* Main Content */}\n        <div className=\"flex-1 flex flex-col\">\n          {/* Header */}\n          <Header\n            onMobileMenuToggle={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          />\n\n        {/* Main Dashboard Content */}\n        <div className=\"flex-1 flex flex-col lg:flex-row\">\n          {/* Center Content */}\n          <div className=\"flex-1 p-4 lg:p-6 space-y-6\">\n            {/* Welcome Section */}\n            <WelcomeSection />\n\n            {/* Analytics Cards */}\n            <AnalyticsCards />\n\n            {/* Featured Courses */}\n            <FeaturedCourses />\n          </div>\n\n          {/* Right Sidebar */}\n          <div className=\"lg:block\">\n            <RightSidebar />\n          </div>\n          </div>\n        </div>\n\n        {/* Toast Notifications */}\n        <Toast\n          message={toast.message}\n          type={toast.type}\n          isVisible={toast.isVisible}\n          onClose={hideToast}\n        />\n      </div>\n    </ErrorBoundary>\n  );\n};\n\nexport default Dashboard;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AACvC,OAAOC,OAAO,MAAM,0BAA0B;AAC9C,OAAOC,MAAM,MAAM,yBAAyB;AAC5C,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,cAAc,MAAM,iCAAiC;AAC5D,OAAOC,eAAe,MAAM,kCAAkC;AAC9D,OAAOC,YAAY,MAAM,+BAA+B;AACxD,OAAOC,aAAa,MAAM,gCAAgC;AAC1D,OAAOC,KAAK,MAAM,wBAAwB;AAC1C,SAASC,QAAQ,QAAQ,sBAAsB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAEhD,MAAMC,SAAmB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAChC,MAAM,CAACC,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGf,QAAQ,CAAC,KAAK,CAAC;EAC/D,MAAM;IAAEgB,KAAK;IAAEC,SAAS;IAAEC;EAAY,CAAC,GAAGT,QAAQ,CAAC,CAAC;;EAEpD;EACAV,KAAK,CAACoB,SAAS,CAAC,MAAM;IACpB,MAAMC,KAAK,GAAGC,UAAU,CAAC,MAAM;MAC7BH,WAAW,CAAC,iCAAiC,CAAC;IAChD,CAAC,EAAE,IAAI,CAAC;IACR,OAAO,MAAMI,YAAY,CAACF,KAAK,CAAC;EAClC,CAAC,EAAE,CAACF,WAAW,CAAC,CAAC;EAEjB,oBACEP,OAAA,CAACJ,aAAa;IAAAgB,QAAA,eACZZ,OAAA;MAAKa,SAAS,EAAC,uCAAuC;MAAAD,QAAA,GAEnDT,gBAAgB,iBACfH,OAAA;QACEa,SAAS,EAAC,qDAAqD;QAC/DC,OAAO,EAAEA,CAAA,KAAMV,mBAAmB,CAAC,KAAK;MAAE;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C,CACF,eAGDlB,OAAA,CAACV,OAAO;QACNa,gBAAgB,EAAEA,gBAAiB;QACnCC,mBAAmB,EAAEA;MAAoB;QAAAW,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC1C,CAAC,eAGFlB,OAAA;QAAKa,SAAS,EAAC,sBAAsB;QAAAD,QAAA,gBAEnCZ,OAAA,CAACT,MAAM;UACL4B,kBAAkB,EAAEA,CAAA,KAAMf,mBAAmB,CAAC,CAACD,gBAAgB;QAAE;UAAAY,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE,CAAC,eAGJlB,OAAA;UAAKa,SAAS,EAAC,kCAAkC;UAAAD,QAAA,gBAE/CZ,OAAA;YAAKa,SAAS,EAAC,6BAA6B;YAAAD,QAAA,gBAE1CZ,OAAA,CAACR,cAAc;cAAAuB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGlBlB,OAAA,CAACP,cAAc;cAAAsB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAGlBlB,OAAA,CAACN,eAAe;cAAAqB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAChB,CAAC,eAGNlB,OAAA;YAAKa,SAAS,EAAC,UAAU;YAAAD,QAAA,eACvBZ,OAAA,CAACL,YAAY;cAAAoB,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACb,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACD,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAGNlB,OAAA,CAACH,KAAK;QACJuB,OAAO,EAAEf,KAAK,CAACe,OAAQ;QACvBC,IAAI,EAAEhB,KAAK,CAACgB,IAAK;QACjBC,SAAS,EAAEjB,KAAK,CAACiB,SAAU;QAC3BC,OAAO,EAAEjB;MAAU;QAAAS,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpB,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACC;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACO,CAAC;AAEpB,CAAC;AAAChB,EAAA,CAnEID,SAAmB;EAAA,QAEmBH,QAAQ;AAAA;AAAA0B,EAAA,GAF9CvB,SAAmB;AAqEzB,eAAeA,SAAS;AAAC,IAAAuB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}