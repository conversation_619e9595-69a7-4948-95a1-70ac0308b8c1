{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\AnalyticsCards.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst AnalyticsCard = ({\n  title,\n  value,\n  subtitle,\n  icon,\n  color,\n  bgColor\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-300 cursor-pointer group\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex items-center justify-between mb-4\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `w-12 h-12 ${bgColor} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`,\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: `text-xl ${color}`,\n          children: icon\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 16,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"text-right\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300\",\n          children: value\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-sm text-gray-500\",\n          children: subtitle\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 21,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 15,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"h3\", {\n      className: \"text-sm font-medium text-gray-600 group-hover:text-gray-800 transition-colors duration-300\",\n      children: title\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 24,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 14,\n    columnNumber: 5\n  }, this);\n};\n_c = AnalyticsCard;\nconst AnalyticsCards = () => {\n  const analyticsData = [{\n    title: 'Weekly Analysis',\n    value: '02',\n    subtitle: '5 Min 10 Sec',\n    icon: '📊',\n    color: 'text-blue-600',\n    bgColor: 'bg-blue-100'\n  }, {\n    title: 'Total Course',\n    value: '04',\n    subtitle: '5 Min 7 Sec',\n    icon: '📚',\n    color: 'text-orange-600',\n    bgColor: 'bg-orange-100'\n  }, {\n    title: 'Course Enroll',\n    value: '3.69k',\n    subtitle: '5 Min 7 Sec',\n    icon: '👥',\n    color: 'text-purple-600',\n    bgColor: 'bg-purple-100'\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"grid grid-cols-1 md:grid-cols-3 gap-6\",\n    children: analyticsData.map((item, index) => /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"animate-slide-up\",\n      style: {\n        animationDelay: `${index * 0.1}s`\n      },\n      children: /*#__PURE__*/_jsxDEV(AnalyticsCard, {\n        ...item\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 61,\n        columnNumber: 11\n      }, this)\n    }, index, false, {\n      fileName: _jsxFileName,\n      lineNumber: 60,\n      columnNumber: 9\n    }, this))\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 58,\n    columnNumber: 5\n  }, this);\n};\n_c2 = AnalyticsCards;\nexport default AnalyticsCards;\nvar _c, _c2;\n$RefreshReg$(_c, \"AnalyticsCard\");\n$RefreshReg$(_c2, \"AnalyticsCards\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "AnalyticsCard", "title", "value", "subtitle", "icon", "color", "bgColor", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "AnalyticsCards", "analyticsData", "map", "item", "index", "style", "animationDelay", "_c2", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/AnalyticsCards.tsx"], "sourcesContent": ["import React from 'react';\n\ninterface AnalyticsCardProps {\n  title: string;\n  value: string;\n  subtitle: string;\n  icon: string;\n  color: string;\n  bgColor: string;\n}\n\nconst AnalyticsCard: React.FC<AnalyticsCardProps> = ({ title, value, subtitle, icon, color, bgColor }) => {\n  return (\n    <div className=\"bg-white rounded-xl p-6 border border-gray-200 hover:shadow-lg hover:border-gray-300 transition-all duration-300 cursor-pointer group\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div className={`w-12 h-12 ${bgColor} rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300`}>\n          <span className={`text-xl ${color}`}>{icon}</span>\n        </div>\n        <div className=\"text-right\">\n          <div className=\"text-2xl font-bold text-gray-900 group-hover:text-blue-600 transition-colors duration-300\">{value}</div>\n          <div className=\"text-sm text-gray-500\">{subtitle}</div>\n        </div>\n      </div>\n      <h3 className=\"text-sm font-medium text-gray-600 group-hover:text-gray-800 transition-colors duration-300\">{title}</h3>\n    </div>\n  );\n};\n\nconst AnalyticsCards: React.FC = () => {\n  const analyticsData = [\n    {\n      title: 'Weekly Analysis',\n      value: '02',\n      subtitle: '5 Min 10 Sec',\n      icon: '📊',\n      color: 'text-blue-600',\n      bgColor: 'bg-blue-100'\n    },\n    {\n      title: 'Total Course',\n      value: '04',\n      subtitle: '5 Min 7 Sec',\n      icon: '📚',\n      color: 'text-orange-600',\n      bgColor: 'bg-orange-100'\n    },\n    {\n      title: 'Course Enroll',\n      value: '3.69k',\n      subtitle: '5 Min 7 Sec',\n      icon: '👥',\n      color: 'text-purple-600',\n      bgColor: 'bg-purple-100'\n    }\n  ];\n\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-6\">\n      {analyticsData.map((item, index) => (\n        <div key={index} className=\"animate-slide-up\" style={{ animationDelay: `${index * 0.1}s` }}>\n          <AnalyticsCard {...item} />\n        </div>\n      ))}\n    </div>\n  );\n};\n\nexport default AnalyticsCards;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAW1B,MAAMC,aAA2C,GAAGA,CAAC;EAAEC,KAAK;EAAEC,KAAK;EAAEC,QAAQ;EAAEC,IAAI;EAAEC,KAAK;EAAEC;AAAQ,CAAC,KAAK;EACxG,oBACEP,OAAA;IAAKQ,SAAS,EAAC,uIAAuI;IAAAC,QAAA,gBACpJT,OAAA;MAAKQ,SAAS,EAAC,wCAAwC;MAAAC,QAAA,gBACrDT,OAAA;QAAKQ,SAAS,EAAE,aAAaD,OAAO,wGAAyG;QAAAE,QAAA,eAC3IT,OAAA;UAAMQ,SAAS,EAAE,WAAWF,KAAK,EAAG;UAAAG,QAAA,EAAEJ;QAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACNb,OAAA;QAAKQ,SAAS,EAAC,YAAY;QAAAC,QAAA,gBACzBT,OAAA;UAAKQ,SAAS,EAAC,2FAA2F;UAAAC,QAAA,EAAEN;QAAK;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACxHb,OAAA;UAAKQ,SAAS,EAAC,uBAAuB;UAAAC,QAAA,EAAEL;QAAQ;UAAAM,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACpD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eACNb,OAAA;MAAIQ,SAAS,EAAC,4FAA4F;MAAAC,QAAA,EAAEP;IAAK;MAAAQ,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAK,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACpH,CAAC;AAEV,CAAC;AAACC,EAAA,GAfIb,aAA2C;AAiBjD,MAAMc,cAAwB,GAAGA,CAAA,KAAM;EACrC,MAAMC,aAAa,GAAG,CACpB;IACEd,KAAK,EAAE,iBAAiB;IACxBC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,cAAc;IACxBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,eAAe;IACtBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,KAAK,EAAE,cAAc;IACrBC,KAAK,EAAE,IAAI;IACXC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE;EACX,CAAC,EACD;IACEL,KAAK,EAAE,eAAe;IACtBC,KAAK,EAAE,OAAO;IACdC,QAAQ,EAAE,aAAa;IACvBC,IAAI,EAAE,IAAI;IACVC,KAAK,EAAE,iBAAiB;IACxBC,OAAO,EAAE;EACX,CAAC,CACF;EAED,oBACEP,OAAA;IAAKQ,SAAS,EAAC,uCAAuC;IAAAC,QAAA,EACnDO,aAAa,CAACC,GAAG,CAAC,CAACC,IAAI,EAAEC,KAAK,kBAC7BnB,OAAA;MAAiBQ,SAAS,EAAC,kBAAkB;MAACY,KAAK,EAAE;QAAEC,cAAc,EAAE,GAAGF,KAAK,GAAG,GAAG;MAAI,CAAE;MAAAV,QAAA,eACzFT,OAAA,CAACC,aAAa;QAAA,GAAKiB;MAAI;QAAAR,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAG;IAAC,GADnBM,KAAK;MAAAT,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAEV,CACN;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACC,CAAC;AAEV,CAAC;AAACS,GAAA,GArCIP,cAAwB;AAuC9B,eAAeA,cAAc;AAAC,IAAAD,EAAA,EAAAQ,GAAA;AAAAC,YAAA,CAAAT,EAAA;AAAAS,YAAA,CAAAD,GAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}