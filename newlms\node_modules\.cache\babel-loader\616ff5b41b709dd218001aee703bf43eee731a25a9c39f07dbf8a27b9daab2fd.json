{"ast": null, "code": "var _s = $RefreshSig$();\nimport { useState, useCallback } from 'react';\nexport const useToast = () => {\n  _s();\n  const [toast, setToast] = useState({\n    message: '',\n    type: 'info',\n    isVisible: false\n  });\n  const showToast = useCallback((message, type = 'info') => {\n    setToast({\n      message,\n      type,\n      isVisible: true\n    });\n  }, []);\n  const hideToast = useCallback(() => {\n    setToast(prev => ({\n      ...prev,\n      isVisible: false\n    }));\n  }, []);\n  const showSuccess = useCallback(message => {\n    showToast(message, 'success');\n  }, [showToast]);\n  const showError = useCallback(message => {\n    showToast(message, 'error');\n  }, [showToast]);\n  const showWarning = useCallback(message => {\n    showToast(message, 'warning');\n  }, [showToast]);\n  const showInfo = useCallback(message => {\n    showToast(message, 'info');\n  }, [showToast]);\n  return {\n    toast,\n    showToast,\n    hideToast,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo\n  };\n};\n_s(useToast, \"UtMVejv2u3V1G+xhXSwFcy0BKBM=\");", "map": {"version": 3, "names": ["useState", "useCallback", "useToast", "_s", "toast", "setToast", "message", "type", "isVisible", "showToast", "hideToast", "prev", "showSuccess", "showError", "showWarning", "showInfo"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/hooks/useToast.tsx"], "sourcesContent": ["import { useState, useCallback } from 'react';\n\ninterface ToastState {\n  message: string;\n  type: 'success' | 'error' | 'warning' | 'info';\n  isVisible: boolean;\n}\n\nexport const useToast = () => {\n  const [toast, setToast] = useState<ToastState>({\n    message: '',\n    type: 'info',\n    isVisible: false\n  });\n\n  const showToast = useCallback((message: string, type: 'success' | 'error' | 'warning' | 'info' = 'info') => {\n    setToast({\n      message,\n      type,\n      isVisible: true\n    });\n  }, []);\n\n  const hideToast = useCallback(() => {\n    setToast(prev => ({\n      ...prev,\n      isVisible: false\n    }));\n  }, []);\n\n  const showSuccess = useCallback((message: string) => {\n    showToast(message, 'success');\n  }, [showToast]);\n\n  const showError = useCallback((message: string) => {\n    showToast(message, 'error');\n  }, [showToast]);\n\n  const showWarning = useCallback((message: string) => {\n    showToast(message, 'warning');\n  }, [showToast]);\n\n  const showInfo = useCallback((message: string) => {\n    showToast(message, 'info');\n  }, [showToast]);\n\n  return {\n    toast,\n    showToast,\n    hideToast,\n    showSuccess,\n    showError,\n    showWarning,\n    showInfo\n  };\n};\n"], "mappings": ";AAAA,SAASA,QAAQ,EAAEC,WAAW,QAAQ,OAAO;AAQ7C,OAAO,MAAMC,QAAQ,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC5B,MAAM,CAACC,KAAK,EAAEC,QAAQ,CAAC,GAAGL,QAAQ,CAAa;IAC7CM,OAAO,EAAE,EAAE;IACXC,IAAI,EAAE,MAAM;IACZC,SAAS,EAAE;EACb,CAAC,CAAC;EAEF,MAAMC,SAAS,GAAGR,WAAW,CAAC,CAACK,OAAe,EAAEC,IAA8C,GAAG,MAAM,KAAK;IAC1GF,QAAQ,CAAC;MACPC,OAAO;MACPC,IAAI;MACJC,SAAS,EAAE;IACb,CAAC,CAAC;EACJ,CAAC,EAAE,EAAE,CAAC;EAEN,MAAME,SAAS,GAAGT,WAAW,CAAC,MAAM;IAClCI,QAAQ,CAACM,IAAI,KAAK;MAChB,GAAGA,IAAI;MACPH,SAAS,EAAE;IACb,CAAC,CAAC,CAAC;EACL,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMI,WAAW,GAAGX,WAAW,CAAEK,OAAe,IAAK;IACnDG,SAAS,CAACH,OAAO,EAAE,SAAS,CAAC;EAC/B,CAAC,EAAE,CAACG,SAAS,CAAC,CAAC;EAEf,MAAMI,SAAS,GAAGZ,WAAW,CAAEK,OAAe,IAAK;IACjDG,SAAS,CAACH,OAAO,EAAE,OAAO,CAAC;EAC7B,CAAC,EAAE,CAACG,SAAS,CAAC,CAAC;EAEf,MAAMK,WAAW,GAAGb,WAAW,CAAEK,OAAe,IAAK;IACnDG,SAAS,CAACH,OAAO,EAAE,SAAS,CAAC;EAC/B,CAAC,EAAE,CAACG,SAAS,CAAC,CAAC;EAEf,MAAMM,QAAQ,GAAGd,WAAW,CAAEK,OAAe,IAAK;IAChDG,SAAS,CAACH,OAAO,EAAE,MAAM,CAAC;EAC5B,CAAC,EAAE,CAACG,SAAS,CAAC,CAAC;EAEf,OAAO;IACLL,KAAK;IACLK,SAAS;IACTC,SAAS;IACTE,WAAW;IACXC,SAAS;IACTC,WAAW;IACXC;EACF,CAAC;AACH,CAAC;AAACZ,EAAA,CA/CWD,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}