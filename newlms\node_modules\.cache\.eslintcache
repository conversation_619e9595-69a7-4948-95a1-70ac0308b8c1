[{"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseDetail.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseBuilder.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\Dashboard.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\WelcomeSection.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Header.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Sidebar.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\AnalyticsCards.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\RightSidebar.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\FeaturedCourses.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\PerformanceChart.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Calendar.tsx": "14"}, {"size": 535, "mtime": 1747410977891, "results": "15", "hashOfConfig": "16"}, {"size": 362, "mtime": 1747410980121, "results": "17", "hashOfConfig": "16"}, {"size": 11312, "mtime": 1748622610502, "results": "18", "hashOfConfig": "16"}, {"size": 33786, "mtime": 1748606052840, "results": "19", "hashOfConfig": "16"}, {"size": 19011, "mtime": 1748545844565, "results": "20", "hashOfConfig": "16"}, {"size": 1226, "mtime": 1748622796500, "results": "21", "hashOfConfig": "16"}, {"size": 2963, "mtime": 1748622940021, "results": "22", "hashOfConfig": "16"}, {"size": 4026, "mtime": 1748622848688, "results": "23", "hashOfConfig": "16"}, {"size": 2934, "mtime": 1748622783553, "results": "24", "hashOfConfig": "16"}, {"size": 2072, "mtime": 1748622964032, "results": "25", "hashOfConfig": "16"}, {"size": 4156, "mtime": 1748622806894, "results": "26", "hashOfConfig": "16"}, {"size": 5821, "mtime": 1748622892287, "results": "27", "hashOfConfig": "16"}, {"size": 3807, "mtime": 1748622928274, "results": "28", "hashOfConfig": "16"}, {"size": 4695, "mtime": 1748622551005, "results": "29", "hashOfConfig": "16"}, {"filePath": "30", "messages": "31", "suppressedMessages": "32", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12e2umx", {"filePath": "33", "messages": "34", "suppressedMessages": "35", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\App.js", ["72", "73", "74", "75", "76", "77", "78", "79", "80", "81", "82", "83", "84", "85", "86"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseDetail.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseBuilder.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\Dashboard.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\WelcomeSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\AnalyticsCards.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\RightSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\FeaturedCourses.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\PerformanceChart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Calendar.tsx", [], [], {"ruleId": "87", "severity": 1, "message": "88", "line": 8, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 8, "endColumn": 15}, {"ruleId": "87", "severity": 1, "message": "91", "line": 14, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 14, "endColumn": 15}, {"ruleId": "87", "severity": 1, "message": "92", "line": 21, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 21, "endColumn": 17}, {"ruleId": "87", "severity": 1, "message": "93", "line": 27, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 27, "endColumn": 19}, {"ruleId": "87", "severity": 1, "message": "94", "line": 33, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 33, "endColumn": 15}, {"ruleId": "87", "severity": 1, "message": "95", "line": 39, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 39, "endColumn": 16}, {"ruleId": "87", "severity": 1, "message": "96", "line": 45, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 45, "endColumn": 19}, {"ruleId": "87", "severity": 1, "message": "97", "line": 51, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 51, "endColumn": 19}, {"ruleId": "87", "severity": 1, "message": "98", "line": 58, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 58, "endColumn": 15}, {"ruleId": "87", "severity": 1, "message": "99", "line": 64, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 64, "endColumn": 17}, {"ruleId": "87", "severity": 1, "message": "100", "line": 70, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 70, "endColumn": 15}, {"ruleId": "87", "severity": 1, "message": "101", "line": 76, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 76, "endColumn": 15}, {"ruleId": "87", "severity": 1, "message": "102", "line": 88, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 88, "endColumn": 22}, {"ruleId": "87", "severity": 1, "message": "103", "line": 101, "column": 7, "nodeType": "89", "messageId": "90", "endLine": 101, "endColumn": 17}, {"ruleId": "87", "severity": 1, "message": "104", "line": 158, "column": 9, "nodeType": "89", "messageId": "90", "endLine": 158, "endColumn": 16}, "no-unused-vars", "'HomeIcon' is assigned a value but never used.", "Identifier", "unusedVar", "'ChatIcon' is assigned a value but never used.", "'FolderIcon' is assigned a value but never used.", "'BookmarkIcon' is assigned a value but never used.", "'UserIcon' is assigned a value but never used.", "'ClockIcon' is assigned a value but never used.", "'DocumentIcon' is assigned a value but never used.", "'SettingsIcon' is assigned a value but never used.", "'PlusIcon' is assigned a value but never used.", "'FilterIcon' is assigned a value but never used.", "'GridIcon' is assigned a value but never used.", "'ListIcon' is assigned a value but never used.", "'ChevronDownIcon' is assigned a value but never used.", "'CourseCard' is assigned a value but never used.", "'courses' is assigned a value but never used."]