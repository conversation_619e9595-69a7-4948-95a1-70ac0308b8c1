[{"C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\index.js": "1", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\App.js": "3", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseDetail.tsx": "4", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseBuilder.tsx": "5", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\Dashboard.tsx": "6", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\WelcomeSection.tsx": "7", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Header.tsx": "8", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Sidebar.tsx": "9", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\AnalyticsCards.tsx": "10", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\RightSidebar.tsx": "11", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\FeaturedCourses.tsx": "12", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\PerformanceChart.tsx": "13", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Calendar.tsx": "14", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\ErrorBoundary.tsx": "15", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\hooks\\useToast.tsx": "16", "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Toast.tsx": "17"}, {"size": 535, "mtime": 1747410977891, "results": "18", "hashOfConfig": "19"}, {"size": 362, "mtime": 1747410980121, "results": "20", "hashOfConfig": "19"}, {"size": 11312, "mtime": 1748622610502, "results": "21", "hashOfConfig": "19"}, {"size": 33786, "mtime": 1748606052840, "results": "22", "hashOfConfig": "19"}, {"size": 19011, "mtime": 1748545844565, "results": "23", "hashOfConfig": "19"}, {"size": 2431, "mtime": 1748626322026, "results": "24", "hashOfConfig": "19"}, {"size": 2963, "mtime": 1748622940021, "results": "25", "hashOfConfig": "19"}, {"size": 9121, "mtime": 1748626071086, "results": "26", "hashOfConfig": "19"}, {"size": 4439, "mtime": 1748626260578, "results": "27", "hashOfConfig": "19"}, {"size": 3127, "mtime": 1748626107739, "results": "28", "hashOfConfig": "19"}, {"size": 4156, "mtime": 1748622806894, "results": "29", "hashOfConfig": "19"}, {"size": 9529, "mtime": 1748626297461, "results": "30", "hashOfConfig": "19"}, {"size": 3807, "mtime": 1748622928274, "results": "31", "hashOfConfig": "19"}, {"size": 4695, "mtime": 1748622551005, "results": "32", "hashOfConfig": "19"}, {"size": 1919, "mtime": 1748625852939, "results": "33", "hashOfConfig": "19"}, {"size": 1188, "mtime": 1748626177345, "results": "34", "hashOfConfig": "19"}, {"size": 3710, "mtime": 1748626167180, "results": "35", "hashOfConfig": "19"}, {"filePath": "36", "messages": "37", "suppressedMessages": "38", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "12e2umx", {"filePath": "39", "messages": "40", "suppressedMessages": "41", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "42", "messages": "43", "suppressedMessages": "44", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 15, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "45", "messages": "46", "suppressedMessages": "47", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "48", "messages": "49", "suppressedMessages": "50", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "51", "messages": "52", "suppressedMessages": "53", "errorCount": 1, "fatalErrorCount": 1, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "54", "messages": "55", "suppressedMessages": "56", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "57", "messages": "58", "suppressedMessages": "59", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "60", "messages": "61", "suppressedMessages": "62", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "63", "messages": "64", "suppressedMessages": "65", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "66", "messages": "67", "suppressedMessages": "68", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "69", "messages": "70", "suppressedMessages": "71", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "72", "messages": "73", "suppressedMessages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "75", "messages": "76", "suppressedMessages": "77", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "78", "messages": "79", "suppressedMessages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "81", "messages": "82", "suppressedMessages": "83", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "84", "messages": "85", "suppressedMessages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\index.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\App.js", ["87", "88", "89", "90", "91", "92", "93", "94", "95", "96", "97", "98", "99", "100", "101"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseDetail.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\CourseBuilder.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\Dashboard.tsx", ["102"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\WelcomeSection.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Header.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Sidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\AnalyticsCards.tsx", ["103"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\RightSidebar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\FeaturedCourses.tsx", ["104"], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\PerformanceChart.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Calendar.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\ErrorBoundary.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\hooks\\useToast.tsx", [], [], "C:\\Users\\<USER>\\Desktop\\Projects\\ReactJs\\newlms\\src\\components\\Toast.tsx", ["105"], [], {"ruleId": "106", "severity": 1, "message": "107", "line": 8, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 8, "endColumn": 15}, {"ruleId": "106", "severity": 1, "message": "110", "line": 14, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 14, "endColumn": 15}, {"ruleId": "106", "severity": 1, "message": "111", "line": 21, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 21, "endColumn": 17}, {"ruleId": "106", "severity": 1, "message": "112", "line": 27, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 27, "endColumn": 19}, {"ruleId": "106", "severity": 1, "message": "113", "line": 33, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 33, "endColumn": 15}, {"ruleId": "106", "severity": 1, "message": "114", "line": 39, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 39, "endColumn": 16}, {"ruleId": "106", "severity": 1, "message": "115", "line": 45, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 45, "endColumn": 19}, {"ruleId": "106", "severity": 1, "message": "116", "line": 51, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 51, "endColumn": 19}, {"ruleId": "106", "severity": 1, "message": "117", "line": 58, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 58, "endColumn": 15}, {"ruleId": "106", "severity": 1, "message": "118", "line": 64, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 64, "endColumn": 17}, {"ruleId": "106", "severity": 1, "message": "119", "line": 70, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 70, "endColumn": 15}, {"ruleId": "106", "severity": 1, "message": "120", "line": 76, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 76, "endColumn": 15}, {"ruleId": "106", "severity": 1, "message": "121", "line": 88, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 88, "endColumn": 22}, {"ruleId": "106", "severity": 1, "message": "122", "line": 101, "column": 7, "nodeType": "108", "messageId": "109", "endLine": 101, "endColumn": 17}, {"ruleId": "106", "severity": 1, "message": "123", "line": 158, "column": 9, "nodeType": "108", "messageId": "109", "endLine": 158, "endColumn": 16}, {"ruleId": null, "fatal": true, "severity": 2, "message": "124", "line": 26, "column": 7, "nodeType": null}, {"ruleId": "125", "severity": 1, "message": "126", "line": 2, "column": 8, "nodeType": "108", "messageId": "109", "endLine": 2, "endColumn": 22}, {"ruleId": "127", "severity": 1, "message": "128", "line": 38, "column": 9, "nodeType": "129", "endLine": 61, "endColumn": 4}, {"ruleId": "125", "severity": 1, "message": "130", "line": 1, "column": 17, "nodeType": "108", "messageId": "109", "endLine": 1, "endColumn": 25}, "no-unused-vars", "'HomeIcon' is assigned a value but never used.", "Identifier", "unusedVar", "'ChatIcon' is assigned a value but never used.", "'FolderIcon' is assigned a value but never used.", "'BookmarkIcon' is assigned a value but never used.", "'UserIcon' is assigned a value but never used.", "'ClockIcon' is assigned a value but never used.", "'DocumentIcon' is assigned a value but never used.", "'SettingsIcon' is assigned a value but never used.", "'PlusIcon' is assigned a value but never used.", "'FilterIcon' is assigned a value but never used.", "'GridIcon' is assigned a value but never used.", "'ListIcon' is assigned a value but never used.", "'ChevronDownIcon' is assigned a value but never used.", "'CourseCard' is assigned a value but never used.", "'courses' is assigned a value but never used.", "Parsing error: JSX element 'div' has no corresponding closing tag.", "@typescript-eslint/no-unused-vars", "'LoadingSpinner' is defined but never used.", "react-hooks/exhaustive-deps", "The 'courses' array makes the dependencies of useMemo Hook (at line 82) change on every render. Move it inside the useMemo callback. Alternatively, wrap the initialization of 'courses' in its own useMemo() Hook.", "VariableDeclarator", "'useState' is defined but never used."]