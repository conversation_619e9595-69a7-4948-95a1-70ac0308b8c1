{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = () => {\n  _s();\n  const [activeItem, setActiveItem] = useState('Overview');\n  const menuItems = [{\n    name: 'Overview',\n    icon: '📊',\n    active: true\n  }, {\n    name: 'Account',\n    icon: '👤',\n    active: false\n  }, {\n    name: 'Notes',\n    icon: '📝',\n    active: false\n  }, {\n    name: 'Community',\n    icon: '👥',\n    active: false\n  }, {\n    name: 'Learning',\n    icon: '📚',\n    active: false\n  }, {\n    name: 'Setting',\n    icon: '⚙️',\n    active: false\n  }];\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"w-64 bg-white border-r border-gray-200 flex flex-col\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center space-x-3\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-white font-bold text-sm\",\n            children: \"\\uD83C\\uDF93\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 21,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-xl font-bold text-gray-900\",\n          children: \"Edulearn\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 23,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 19,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 18,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"flex-1 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"space-y-2\",\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => setActiveItem(item.name),\n            className: `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${activeItem === item.name ? 'bg-blue-50 text-blue-700 border border-blue-200' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 40,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 41,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 32,\n            columnNumber: 15\n          }, this)\n        }, item.name, false, {\n          fileName: _jsxFileName,\n          lineNumber: 31,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 29,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 28,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-br from-blue-600 to-purple-700 rounded-xl p-4 text-white relative overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: \"\\uD83D\\uDCDA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 54,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 53,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-sm\",\n                children: \"Get Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 57,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-blue-100\",\n                children: \"Lot of services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 58,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 56,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 52,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full bg-white text-blue-600 font-semibold py-2 px-4 rounded-lg text-sm hover:bg-blue-50 transition-colors\",\n            children: \"Subscribe Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 61,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 51,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-4 -right-4 w-16 h-16 bg-white/10 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 65,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-2 -left-2 w-12 h-12 bg-white/10 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 66,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 50,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 49,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 16,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"X3St0ypmsNxs9IFk/WlrILPBwhk=\");\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Sidebar", "_s", "activeItem", "setActiveItem", "menuItems", "name", "icon", "active", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "map", "item", "onClick", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/Sidebar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\nconst Sidebar: React.FC = () => {\n  const [activeItem, setActiveItem] = useState('Overview');\n\n  const menuItems = [\n    { name: 'Overview', icon: '📊', active: true },\n    { name: 'Account', icon: '👤', active: false },\n    { name: 'Notes', icon: '📝', active: false },\n    { name: 'Community', icon: '👥', active: false },\n    { name: 'Learning', icon: '📚', active: false },\n    { name: 'Setting', icon: '⚙️', active: false },\n  ];\n\n  return (\n    <div className=\"w-64 bg-white border-r border-gray-200 flex flex-col\">\n      {/* Logo */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center space-x-3\">\n          <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n            <span className=\"text-white font-bold text-sm\">🎓</span>\n          </div>\n          <span className=\"text-xl font-bold text-gray-900\">Edulearn</span>\n        </div>\n      </div>\n\n      {/* Navigation Menu */}\n      <nav className=\"flex-1 p-4\">\n        <ul className=\"space-y-2\">\n          {menuItems.map((item) => (\n            <li key={item.name}>\n              <button\n                onClick={() => setActiveItem(item.name)}\n                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${\n                  activeItem === item.name\n                    ? 'bg-blue-50 text-blue-700 border border-blue-200'\n                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                }`}\n              >\n                <span className=\"text-lg\">{item.icon}</span>\n                <span className=\"font-medium\">{item.name}</span>\n              </button>\n            </li>\n          ))}\n        </ul>\n      </nav>\n\n      {/* Premium Section */}\n      <div className=\"p-4\">\n        <div className=\"bg-gradient-to-br from-blue-600 to-purple-700 rounded-xl p-4 text-white relative overflow-hidden\">\n          <div className=\"relative z-10\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <div className=\"w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center\">\n                <span className=\"text-2xl\">📚</span>\n              </div>\n              <div>\n                <h3 className=\"font-bold text-sm\">Get Premium</h3>\n                <p className=\"text-xs text-blue-100\">Lot of services</p>\n              </div>\n            </div>\n            <button className=\"w-full bg-white text-blue-600 font-semibold py-2 px-4 rounded-lg text-sm hover:bg-blue-50 transition-colors\">\n              Subscribe Now\n            </button>\n          </div>\n          <div className=\"absolute -top-4 -right-4 w-16 h-16 bg-white/10 rounded-full\"></div>\n          <div className=\"absolute -bottom-2 -left-2 w-12 h-12 bg-white/10 rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAExC,MAAMC,OAAiB,GAAGA,CAAA,KAAM;EAAAC,EAAA;EAC9B,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGN,QAAQ,CAAC,UAAU,CAAC;EAExD,MAAMO,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,EAC9C;IAAEF,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC9C;IAAEF,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC5C;IAAEF,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM,CAAC,EAChD;IAAEF,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC/C;IAAEF,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM,CAAC,CAC/C;EAED,oBACER,OAAA;IAAKS,SAAS,EAAC,sDAAsD;IAAAC,QAAA,gBAEnEV,OAAA;MAAKS,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3CV,OAAA;QAAKS,SAAS,EAAC,6BAA6B;QAAAC,QAAA,gBAC1CV,OAAA;UAAKS,SAAS,EAAC,iEAAiE;UAAAC,QAAA,eAC9EV,OAAA;YAAMS,SAAS,EAAC,8BAA8B;YAAAC,QAAA,EAAC;UAAE;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrD,CAAC,eACNd,OAAA;UAAMS,SAAS,EAAC,iCAAiC;UAAAC,QAAA,EAAC;QAAQ;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNd,OAAA;MAAKS,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBV,OAAA;QAAIS,SAAS,EAAC,WAAW;QAAAC,QAAA,EACtBL,SAAS,CAACU,GAAG,CAAEC,IAAI,iBAClBhB,OAAA;UAAAU,QAAA,eACEV,OAAA;YACEiB,OAAO,EAAEA,CAAA,KAAMb,aAAa,CAACY,IAAI,CAACV,IAAI,CAAE;YACxCG,SAAS,EAAE,uFACTN,UAAU,KAAKa,IAAI,CAACV,IAAI,GACpB,iDAAiD,GACjD,oDAAoD,EACvD;YAAAI,QAAA,gBAEHV,OAAA;cAAMS,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEM,IAAI,CAACT;YAAI;cAAAI,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5Cd,OAAA;cAAMS,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEM,IAAI,CAACV;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC,GAXFE,IAAI,CAACV,IAAI;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYd,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGNd,OAAA;MAAKS,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClBV,OAAA;QAAKS,SAAS,EAAC,kGAAkG;QAAAC,QAAA,gBAC/GV,OAAA;UAAKS,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5BV,OAAA;YAAKS,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/CV,OAAA;cAAKS,SAAS,EAAC,mEAAmE;cAAAC,QAAA,eAChFV,OAAA;gBAAMS,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACNd,OAAA;cAAAU,QAAA,gBACEV,OAAA;gBAAIS,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDd,OAAA;gBAAGS,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNd,OAAA;YAAQS,SAAS,EAAC,6GAA6G;YAAAC,QAAA,EAAC;UAEhI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNd,OAAA;UAAKS,SAAS,EAAC;QAA6D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnFd,OAAA;UAAKS,SAAS,EAAC;QAA+D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACZ,EAAA,CApEID,OAAiB;AAAAiB,EAAA,GAAjBjB,OAAiB;AAsEvB,eAAeA,OAAO;AAAC,IAAAiB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}