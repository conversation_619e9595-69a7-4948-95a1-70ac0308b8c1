import React from 'react';

const WelcomeSection: React.FC = () => {
  return (
    <div className="bg-gradient-to-r from-orange-400 to-orange-500 rounded-2xl p-8 text-white relative overflow-hidden animate-fade-in">
      {/* Background decorative elements */}
      <div className="absolute top-4 right-8 w-16 h-16 bg-white/10 rounded-full"></div>
      <div className="absolute top-12 right-16 w-8 h-8 bg-white/20 rounded-full"></div>
      <div className="absolute bottom-4 right-4 w-12 h-12 bg-white/10 rounded-full"></div>

      <div className="relative z-10 flex items-center justify-between">
        {/* Left Content */}
        <div className="flex-1">
          <div className="flex items-center space-x-2 mb-2">
            <span className="text-sm font-medium">Welcome Back 👋</span>
          </div>
          <h2 className="text-3xl font-bold mb-2"><PERSON></h2>
          <p className="text-orange-100 mb-6">Go back to the course</p>

          <button className="bg-white text-orange-500 font-semibold px-6 py-3 rounded-lg hover:bg-orange-50 transition-colors">
            Continue Learning
          </button>
        </div>

        {/* Right Illustration */}
        <div className="hidden lg:block relative">
          <div className="flex items-center space-x-4">
            {/* Person 1 */}
            <div className="relative">
              <div className="w-24 h-24 bg-white/20 rounded-full flex items-center justify-center">
                <div className="w-16 h-16 bg-white/30 rounded-full flex items-center justify-center">
                  <span className="text-2xl">👨‍💻</span>
                </div>
              </div>
              {/* Laptop */}
              <div className="absolute -bottom-2 -right-2 w-8 h-6 bg-gray-700 rounded-sm">
                <div className="w-full h-3 bg-gray-600 rounded-t-sm"></div>
              </div>
            </div>

            {/* Person 2 */}
            <div className="relative">
              <div className="w-20 h-20 bg-white/20 rounded-full flex items-center justify-center">
                <div className="w-14 h-14 bg-white/30 rounded-full flex items-center justify-center">
                  <span className="text-xl">👩‍🎓</span>
                </div>
              </div>
              {/* Book */}
              <div className="absolute -bottom-1 -right-1 w-6 h-4 bg-blue-600 rounded-sm">
                <div className="w-full h-1 bg-blue-500 rounded-t-sm"></div>
              </div>
            </div>
          </div>

          {/* Floating elements */}
          <div className="absolute -top-4 left-4 w-6 h-6 bg-yellow-300 rounded-full opacity-80"></div>
          <div className="absolute top-8 -right-2 w-4 h-4 bg-pink-300 rounded-full opacity-80"></div>
          <div className="absolute -bottom-2 left-8 w-5 h-5 bg-green-300 rounded-full opacity-80"></div>
        </div>
      </div>
    </div>
  );
};

export default WelcomeSection;
