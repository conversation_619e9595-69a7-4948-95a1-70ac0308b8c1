import type { ContextOptions, DateArg } from "./types.js";
/**
 * The {@link isThisQuarter} function options.
 */
export interface IsThisQuarterOptions extends ContextOptions<Date> {}
/**
 * @name isThisQuarter
 * @category Quarter Helpers
 * @summary Is the given date in the same quarter as the current date?
 * @pure false
 *
 * @description
 * Is the given date in the same quarter as the current date?
 *
 * @param date - The date to check
 * @param options - An object with options
 *
 * @returns The date is in this quarter
 *
 * @example
 * // If today is 25 September 2014, is 2 July 2014 in this quarter?
 * const result = isThisQuarter(new Date(2014, 6, 2))
 * //=> true
 */
export declare function isThisQuarter(
  date: DateArg<Date> & {},
  options?: IsThisQuarterOptions,
): boolean;
