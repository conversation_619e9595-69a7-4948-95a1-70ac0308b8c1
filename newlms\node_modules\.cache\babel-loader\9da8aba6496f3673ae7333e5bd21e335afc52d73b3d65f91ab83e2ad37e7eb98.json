{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\Sidebar.tsx\",\n  _s = $RefreshSig$();\nimport React, { useState } from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Sidebar = ({\n  isMobileMenuOpen,\n  setIsMobileMenuOpen\n}) => {\n  _s();\n  const [activeItem, setActiveItem] = useState('Overview');\n  const menuItems = [{\n    name: 'Overview',\n    icon: '📊',\n    active: true\n  }, {\n    name: 'Account',\n    icon: '👤',\n    active: false\n  }, {\n    name: 'Notes',\n    icon: '📝',\n    active: false\n  }, {\n    name: 'Community',\n    icon: '👥',\n    active: false\n  }, {\n    name: 'Learning',\n    icon: '📚',\n    active: false\n  }, {\n    name: 'Setting',\n    icon: '⚙️',\n    active: false\n  }];\n  const handleMenuItemClick = itemName => {\n    setActiveItem(itemName);\n    setIsMobileMenuOpen(false); // Close mobile menu when item is clicked\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: `\n      w-64 bg-white border-r border-gray-200 flex flex-col fixed lg:static inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out\n      ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\n      lg:flex\n    `,\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-6 border-b border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex items-center justify-between\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-3\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\",\n            children: /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-white font-bold text-sm\",\n              children: \"\\uD83C\\uDF93\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 35,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-xl font-bold text-gray-900\",\n            children: \"Edulearn\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 38,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 34,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setIsMobileMenuOpen(false),\n          className: \"lg:hidden p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\",\n          children: /*#__PURE__*/_jsxDEV(\"svg\", {\n            className: \"h-6 w-6\",\n            fill: \"none\",\n            stroke: \"currentColor\",\n            viewBox: \"0 0 24 24\",\n            children: /*#__PURE__*/_jsxDEV(\"path\", {\n              strokeLinecap: \"round\",\n              strokeLinejoin: \"round\",\n              strokeWidth: 2,\n              d: \"M6 18L18 6M6 6l12 12\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 47,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 46,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 42,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 33,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 32,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"nav\", {\n      className: \"flex-1 p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"ul\", {\n        className: \"space-y-2\",\n        children: menuItems.map(item => /*#__PURE__*/_jsxDEV(\"li\", {\n          children: /*#__PURE__*/_jsxDEV(\"button\", {\n            onClick: () => handleMenuItemClick(item.name),\n            className: `w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${activeItem === item.name ? 'bg-blue-50 text-blue-700 border border-blue-200' : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}`,\n            children: [/*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"text-lg\",\n              children: item.icon\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 66,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n              className: \"font-medium\",\n              children: item.name\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 67,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 58,\n            columnNumber: 15\n          }, this)\n        }, item.name, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 55,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 54,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-4\",\n      children: /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"bg-gradient-to-br from-blue-600 to-purple-700 rounded-xl p-4 text-white relative overflow-hidden\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"relative z-10\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-3 mb-3\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                className: \"text-2xl\",\n                children: \"\\uD83D\\uDCDA\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 80,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 79,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              children: [/*#__PURE__*/_jsxDEV(\"h3\", {\n                className: \"font-bold text-sm\",\n                children: \"Get Premium\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 83,\n                columnNumber: 17\n              }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n                className: \"text-xs text-blue-100\",\n                children: \"Lot of services\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 84,\n                columnNumber: 17\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 82,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 78,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n            className: \"w-full bg-white text-blue-600 font-semibold py-2 px-4 rounded-lg text-sm hover:bg-blue-50 transition-colors\",\n            children: \"Subscribe Now\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 87,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 77,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-4 -right-4 w-16 h-16 bg-white/10 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 91,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-2 -left-2 w-12 h-12 bg-white/10 rounded-full\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 92,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 76,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 75,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 26,\n    columnNumber: 5\n  }, this);\n};\n_s(Sidebar, \"X3St0ypmsNxs9IFk/WlrILPBwhk=\");\n_c = Sidebar;\nexport default Sidebar;\nvar _c;\n$RefreshReg$(_c, \"Sidebar\");", "map": {"version": 3, "names": ["React", "useState", "jsxDEV", "_jsxDEV", "Sidebar", "isMobileMenuOpen", "setIsMobileMenuOpen", "_s", "activeItem", "setActiveItem", "menuItems", "name", "icon", "active", "handleMenuItemClick", "itemName", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "onClick", "fill", "stroke", "viewBox", "strokeLinecap", "strokeLinejoin", "strokeWidth", "d", "map", "item", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/Sidebar.tsx"], "sourcesContent": ["import React, { useState } from 'react';\n\ninterface SidebarProps {\n  isMobileMenuOpen: boolean;\n  setIsMobileMenuOpen: (open: boolean) => void;\n}\n\nconst Sidebar: React.FC<SidebarProps> = ({ isMobileMenuOpen, setIsMobileMenuOpen }) => {\n  const [activeItem, setActiveItem] = useState('Overview');\n\n  const menuItems = [\n    { name: 'Overview', icon: '📊', active: true },\n    { name: 'Account', icon: '👤', active: false },\n    { name: 'Notes', icon: '📝', active: false },\n    { name: 'Community', icon: '👥', active: false },\n    { name: 'Learning', icon: '📚', active: false },\n    { name: 'Setting', icon: '⚙️', active: false },\n  ];\n\n  const handleMenuItemClick = (itemName: string) => {\n    setActiveItem(itemName);\n    setIsMobileMenuOpen(false); // Close mobile menu when item is clicked\n  };\n\n  return (\n    <div className={`\n      w-64 bg-white border-r border-gray-200 flex flex-col fixed lg:static inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out\n      ${isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}\n      lg:flex\n    `}>\n      {/* Logo */}\n      <div className=\"p-6 border-b border-gray-200\">\n        <div className=\"flex items-center justify-between\">\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center\">\n              <span className=\"text-white font-bold text-sm\">🎓</span>\n            </div>\n            <span className=\"text-xl font-bold text-gray-900\">Edulearn</span>\n          </div>\n\n          {/* Close button for mobile */}\n          <button\n            onClick={() => setIsMobileMenuOpen(false)}\n            className=\"lg:hidden p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-lg\"\n          >\n            <svg className=\"h-6 w-6\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M6 18L18 6M6 6l12 12\" />\n            </svg>\n          </button>\n        </div>\n      </div>\n\n      {/* Navigation Menu */}\n      <nav className=\"flex-1 p-4\">\n        <ul className=\"space-y-2\">\n          {menuItems.map((item) => (\n            <li key={item.name}>\n              <button\n                onClick={() => handleMenuItemClick(item.name)}\n                className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg text-left transition-colors ${\n                  activeItem === item.name\n                    ? 'bg-blue-50 text-blue-700 border border-blue-200'\n                    : 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'\n                }`}\n              >\n                <span className=\"text-lg\">{item.icon}</span>\n                <span className=\"font-medium\">{item.name}</span>\n              </button>\n            </li>\n          ))}\n        </ul>\n      </nav>\n\n      {/* Premium Section */}\n      <div className=\"p-4\">\n        <div className=\"bg-gradient-to-br from-blue-600 to-purple-700 rounded-xl p-4 text-white relative overflow-hidden\">\n          <div className=\"relative z-10\">\n            <div className=\"flex items-center space-x-3 mb-3\">\n              <div className=\"w-12 h-12 bg-white/20 rounded-lg flex items-center justify-center\">\n                <span className=\"text-2xl\">📚</span>\n              </div>\n              <div>\n                <h3 className=\"font-bold text-sm\">Get Premium</h3>\n                <p className=\"text-xs text-blue-100\">Lot of services</p>\n              </div>\n            </div>\n            <button className=\"w-full bg-white text-blue-600 font-semibold py-2 px-4 rounded-lg text-sm hover:bg-blue-50 transition-colors\">\n              Subscribe Now\n            </button>\n          </div>\n          <div className=\"absolute -top-4 -right-4 w-16 h-16 bg-white/10 rounded-full\"></div>\n          <div className=\"absolute -bottom-2 -left-2 w-12 h-12 bg-white/10 rounded-full\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default Sidebar;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAOxC,MAAMC,OAA+B,GAAGA,CAAC;EAAEC,gBAAgB;EAAEC;AAAoB,CAAC,KAAK;EAAAC,EAAA;EACrF,MAAM,CAACC,UAAU,EAAEC,aAAa,CAAC,GAAGR,QAAQ,CAAC,UAAU,CAAC;EAExD,MAAMS,SAAS,GAAG,CAChB;IAAEC,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAK,CAAC,EAC9C;IAAEF,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC9C;IAAEF,IAAI,EAAE,OAAO;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC5C;IAAEF,IAAI,EAAE,WAAW;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM,CAAC,EAChD;IAAEF,IAAI,EAAE,UAAU;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM,CAAC,EAC/C;IAAEF,IAAI,EAAE,SAAS;IAAEC,IAAI,EAAE,IAAI;IAAEC,MAAM,EAAE;EAAM,CAAC,CAC/C;EAED,MAAMC,mBAAmB,GAAIC,QAAgB,IAAK;IAChDN,aAAa,CAACM,QAAQ,CAAC;IACvBT,mBAAmB,CAAC,KAAK,CAAC,CAAC,CAAC;EAC9B,CAAC;EAED,oBACEH,OAAA;IAAKa,SAAS,EAAE;AACpB;AACA,QAAQX,gBAAgB,GAAG,eAAe,GAAG,oCAAoC;AACjF;AACA,KAAM;IAAAY,QAAA,gBAEAd,OAAA;MAAKa,SAAS,EAAC,8BAA8B;MAAAC,QAAA,eAC3Cd,OAAA;QAAKa,SAAS,EAAC,mCAAmC;QAAAC,QAAA,gBAChDd,OAAA;UAAKa,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAC1Cd,OAAA;YAAKa,SAAS,EAAC,iEAAiE;YAAAC,QAAA,eAC9Ed,OAAA;cAAMa,SAAS,EAAC,8BAA8B;cAAAC,QAAA,EAAC;YAAE;cAAAC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAM;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrD,CAAC,eACNlB,OAAA;YAAMa,SAAS,EAAC,iCAAiC;YAAAC,QAAA,EAAC;UAAQ;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC9D,CAAC,eAGNlB,OAAA;UACEmB,OAAO,EAAEA,CAAA,KAAMhB,mBAAmB,CAAC,KAAK,CAAE;UAC1CU,SAAS,EAAC,oIAAoI;UAAAC,QAAA,eAE9Id,OAAA;YAAKa,SAAS,EAAC,SAAS;YAACO,IAAI,EAAC,MAAM;YAACC,MAAM,EAAC,cAAc;YAACC,OAAO,EAAC,WAAW;YAAAR,QAAA,eAC5Ed,OAAA;cAAMuB,aAAa,EAAC,OAAO;cAACC,cAAc,EAAC,OAAO;cAACC,WAAW,EAAE,CAAE;cAACC,CAAC,EAAC;YAAsB;cAAAX,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3F;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC,eAGNlB,OAAA;MAAKa,SAAS,EAAC,YAAY;MAAAC,QAAA,eACzBd,OAAA;QAAIa,SAAS,EAAC,WAAW;QAAAC,QAAA,EACtBP,SAAS,CAACoB,GAAG,CAAEC,IAAI,iBAClB5B,OAAA;UAAAc,QAAA,eACEd,OAAA;YACEmB,OAAO,EAAEA,CAAA,KAAMR,mBAAmB,CAACiB,IAAI,CAACpB,IAAI,CAAE;YAC9CK,SAAS,EAAE,uFACTR,UAAU,KAAKuB,IAAI,CAACpB,IAAI,GACpB,iDAAiD,GACjD,oDAAoD,EACvD;YAAAM,QAAA,gBAEHd,OAAA;cAAMa,SAAS,EAAC,SAAS;cAAAC,QAAA,EAAEc,IAAI,CAACnB;YAAI;cAAAM,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC,eAC5ClB,OAAA;cAAMa,SAAS,EAAC,aAAa;cAAAC,QAAA,EAAEc,IAAI,CAACpB;YAAI;cAAAO,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAO,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC,GAXFU,IAAI,CAACpB,IAAI;UAAAO,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAYd,CACL;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACA;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,eAGNlB,OAAA;MAAKa,SAAS,EAAC,KAAK;MAAAC,QAAA,eAClBd,OAAA;QAAKa,SAAS,EAAC,kGAAkG;QAAAC,QAAA,gBAC/Gd,OAAA;UAAKa,SAAS,EAAC,eAAe;UAAAC,QAAA,gBAC5Bd,OAAA;YAAKa,SAAS,EAAC,kCAAkC;YAAAC,QAAA,gBAC/Cd,OAAA;cAAKa,SAAS,EAAC,mEAAmE;cAAAC,QAAA,eAChFd,OAAA;gBAAMa,SAAS,EAAC,UAAU;gBAAAC,QAAA,EAAC;cAAE;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACjC,CAAC,eACNlB,OAAA;cAAAc,QAAA,gBACEd,OAAA;gBAAIa,SAAS,EAAC,mBAAmB;gBAAAC,QAAA,EAAC;cAAW;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAI,CAAC,eAClDlB,OAAA;gBAAGa,SAAS,EAAC,uBAAuB;gBAAAC,QAAA,EAAC;cAAe;gBAAAC,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAG,CAAC;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACrD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eACNlB,OAAA;YAAQa,SAAS,EAAC,6GAA6G;YAAAC,QAAA,EAAC;UAEhI;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACN,CAAC,eACNlB,OAAA;UAAKa,SAAS,EAAC;QAA6D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACnFlB,OAAA;UAAKa,SAAS,EAAC;QAA+D;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACd,EAAA,CAzFIH,OAA+B;AAAA4B,EAAA,GAA/B5B,OAA+B;AA2FrC,eAAeA,OAAO;AAAC,IAAA4B,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}