{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projects\\\\ReactJs\\\\newlms\\\\src\\\\components\\\\WelcomeSection.tsx\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst WelcomeSection = () => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"bg-gradient-to-r from-orange-400 to-orange-500 rounded-2xl p-8 text-white relative overflow-hidden animate-fade-in\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-4 right-8 w-16 h-16 bg-white/10 rounded-full\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 7,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute top-12 right-16 w-8 h-8 bg-white/20 rounded-full\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 8,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"absolute bottom-4 right-4 w-12 h-12 bg-white/10 rounded-full\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 9,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"relative z-10 flex items-center justify-between\",\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-2 mb-2\",\n          children: /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"text-sm font-medium\",\n            children: \"Welcome Back \\uD83D\\uDC4B\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 15,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 14,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"h2\", {\n          className: \"text-3xl font-bold mb-2\",\n          children: \"Corey George\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 17,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-orange-100 mb-6\",\n          children: \"Go back to the course\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 18,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n          className: \"bg-white text-orange-500 font-semibold px-6 py-3 rounded-lg hover:bg-orange-50 transition-colors\",\n          children: \"Continue Learning\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 20,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 13,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"hidden lg:block relative\",\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center space-x-4\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-24 h-24 bg-white/20 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-16 h-16 bg-white/30 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-2xl\",\n                  children: \"\\uD83D\\uDC68\\u200D\\uD83D\\uDCBB\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 32,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 31,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 30,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-2 -right-2 w-8 h-6 bg-gray-700 rounded-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full h-3 bg-gray-600 rounded-t-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 37,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 36,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 29,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"relative\",\n            children: [/*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-14 h-14 bg-white/30 rounded-full flex items-center justify-center\",\n                children: /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"text-xl\",\n                  children: \"\\uD83D\\uDC69\\u200D\\uD83C\\uDF93\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 45,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 44,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 43,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n              className: \"absolute -bottom-1 -right-1 w-6 h-4 bg-blue-600 rounded-sm\",\n              children: /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"w-full h-1 bg-blue-500 rounded-t-sm\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 50,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 49,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 42,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 27,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -top-4 left-4 w-6 h-6 bg-yellow-300 rounded-full opacity-80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 56,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute top-8 -right-2 w-4 h-4 bg-pink-300 rounded-full opacity-80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 57,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"absolute -bottom-2 left-8 w-5 h-5 bg-green-300 rounded-full opacity-80\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 58,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 26,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 11,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = WelcomeSection;\nexport default WelcomeSection;\nvar _c;\n$RefreshReg$(_c, \"WelcomeSection\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "WelcomeSection", "className", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Desktop/Projects/ReactJs/newlms/src/components/WelcomeSection.tsx"], "sourcesContent": ["import React from 'react';\n\nconst WelcomeSection: React.FC = () => {\n  return (\n    <div className=\"bg-gradient-to-r from-orange-400 to-orange-500 rounded-2xl p-8 text-white relative overflow-hidden animate-fade-in\">\n      {/* Background decorative elements */}\n      <div className=\"absolute top-4 right-8 w-16 h-16 bg-white/10 rounded-full\"></div>\n      <div className=\"absolute top-12 right-16 w-8 h-8 bg-white/20 rounded-full\"></div>\n      <div className=\"absolute bottom-4 right-4 w-12 h-12 bg-white/10 rounded-full\"></div>\n\n      <div className=\"relative z-10 flex items-center justify-between\">\n        {/* Left Content */}\n        <div className=\"flex-1\">\n          <div className=\"flex items-center space-x-2 mb-2\">\n            <span className=\"text-sm font-medium\">Welcome Back 👋</span>\n          </div>\n          <h2 className=\"text-3xl font-bold mb-2\"><PERSON></h2>\n          <p className=\"text-orange-100 mb-6\">Go back to the course</p>\n\n          <button className=\"bg-white text-orange-500 font-semibold px-6 py-3 rounded-lg hover:bg-orange-50 transition-colors\">\n            Continue Learning\n          </button>\n        </div>\n\n        {/* Right Illustration */}\n        <div className=\"hidden lg:block relative\">\n          <div className=\"flex items-center space-x-4\">\n            {/* Person 1 */}\n            <div className=\"relative\">\n              <div className=\"w-24 h-24 bg-white/20 rounded-full flex items-center justify-center\">\n                <div className=\"w-16 h-16 bg-white/30 rounded-full flex items-center justify-center\">\n                  <span className=\"text-2xl\">👨‍💻</span>\n                </div>\n              </div>\n              {/* Laptop */}\n              <div className=\"absolute -bottom-2 -right-2 w-8 h-6 bg-gray-700 rounded-sm\">\n                <div className=\"w-full h-3 bg-gray-600 rounded-t-sm\"></div>\n              </div>\n            </div>\n\n            {/* Person 2 */}\n            <div className=\"relative\">\n              <div className=\"w-20 h-20 bg-white/20 rounded-full flex items-center justify-center\">\n                <div className=\"w-14 h-14 bg-white/30 rounded-full flex items-center justify-center\">\n                  <span className=\"text-xl\">👩‍🎓</span>\n                </div>\n              </div>\n              {/* Book */}\n              <div className=\"absolute -bottom-1 -right-1 w-6 h-4 bg-blue-600 rounded-sm\">\n                <div className=\"w-full h-1 bg-blue-500 rounded-t-sm\"></div>\n              </div>\n            </div>\n          </div>\n\n          {/* Floating elements */}\n          <div className=\"absolute -top-4 left-4 w-6 h-6 bg-yellow-300 rounded-full opacity-80\"></div>\n          <div className=\"absolute top-8 -right-2 w-4 h-4 bg-pink-300 rounded-full opacity-80\"></div>\n          <div className=\"absolute -bottom-2 left-8 w-5 h-5 bg-green-300 rounded-full opacity-80\"></div>\n        </div>\n      </div>\n    </div>\n  );\n};\n\nexport default WelcomeSection;\n"], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,cAAwB,GAAGA,CAAA,KAAM;EACrC,oBACED,OAAA;IAAKE,SAAS,EAAC,oHAAoH;IAAAC,QAAA,gBAEjIH,OAAA;MAAKE,SAAS,EAAC;IAA2D;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACjFP,OAAA;MAAKE,SAAS,EAAC;IAA2D;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eACjFP,OAAA;MAAKE,SAAS,EAAC;IAA8D;MAAAE,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAM,CAAC,eAEpFP,OAAA;MAAKE,SAAS,EAAC,iDAAiD;MAAAC,QAAA,gBAE9DH,OAAA;QAAKE,SAAS,EAAC,QAAQ;QAAAC,QAAA,gBACrBH,OAAA;UAAKE,SAAS,EAAC,kCAAkC;UAAAC,QAAA,eAC/CH,OAAA;YAAME,SAAS,EAAC,qBAAqB;YAAAC,QAAA,EAAC;UAAe;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACzD,CAAC,eACNP,OAAA;UAAIE,SAAS,EAAC,yBAAyB;UAAAC,QAAA,EAAC;QAAY;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAI,CAAC,eACzDP,OAAA;UAAGE,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAqB;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAG,CAAC,eAE7DP,OAAA;UAAQE,SAAS,EAAC,kGAAkG;UAAAC,QAAA,EAAC;QAErH;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACN,CAAC,eAGNP,OAAA;QAAKE,SAAS,EAAC,0BAA0B;QAAAC,QAAA,gBACvCH,OAAA;UAAKE,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBAE1CH,OAAA;YAAKE,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBH,OAAA;cAAKE,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClFH,OAAA;gBAAKE,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,eAClFH,OAAA;kBAAME,SAAS,EAAC,UAAU;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACpC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,4DAA4D;cAAAC,QAAA,eACzEH,OAAA;gBAAKE,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAGNP,OAAA;YAAKE,SAAS,EAAC,UAAU;YAAAC,QAAA,gBACvBH,OAAA;cAAKE,SAAS,EAAC,qEAAqE;cAAAC,QAAA,eAClFH,OAAA;gBAAKE,SAAS,EAAC,qEAAqE;gBAAAC,QAAA,eAClFH,OAAA;kBAAME,SAAS,EAAC,SAAS;kBAAAC,QAAA,EAAC;gBAAK;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAM;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACnC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACH,CAAC,eAENP,OAAA;cAAKE,SAAS,EAAC,4DAA4D;cAAAC,QAAA,eACzEH,OAAA;gBAAKE,SAAS,EAAC;cAAqC;gBAAAE,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxD,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAGNP,OAAA;UAAKE,SAAS,EAAC;QAAsE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC5FP,OAAA;UAAKE,SAAS,EAAC;QAAqE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eAC3FP,OAAA;UAAKE,SAAS,EAAC;QAAwE;UAAAE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3F,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACH,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACC,EAAA,GA5DIP,cAAwB;AA8D9B,eAAeA,cAAc;AAAC,IAAAO,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}